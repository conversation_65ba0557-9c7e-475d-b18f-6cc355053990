# VS Code Entry Points and Startup Flow Documentation

## Overview

This document explains how VS Code starts up in different environments, tracing the entry points and execution flow from the compiled output directory (`out/`).

## Desktop/Electron Version

### Startup Command
```bash
npm run compile  # Compiles TypeScript source to out/ directory
./scripts/code.sh  # Starts desktop VS Code
```

### Multi-Process Architecture

VS Code desktop uses Electron's multi-process architecture:

#### 1. Main Process Entry Point
- **File**: `out/main.js` (48KB, 623 lines)
- **Called by**: Node.js runtime when executing `./scripts/code.sh`
- **Role**: Primary Electron main process entry point
- **Responsibilities**:
  - Sets up Electron environment
  - Configures security policies
  - Manages crash reporting
  - Creates and manages renderer processes

#### 2. Application Startup Flow
```
Node.js Runtime
    ↓
out/main.js
    ↓
out/vs/code/electron-main/main.js
    ↓
out/vs/code/electron-main/app.js (CodeApplication class, 1221 lines)
    ↓
Creates Browser Windows
    ↓
Loads: out/vs/code/electron-browser/workbench/workbench.html
    ↓
Executes: out/vs/code/electron-browser/workbench/workbench.js (46KB, 453 lines)
```

#### 3. Renderer Process Entry Point
- **File**: `out/vs/code/electron-browser/workbench/workbench.js`
- **Loaded by**: `workbench.html` as ES module
- **Role**: Workbench UI renderer entry point
- **Responsibilities**:
  - Renders VS Code UI components
  - Handles user interactions
  - Manages editor instances
  - Coordinates with main process via IPC

### Key Files Structure
```
out/
├── main.js                                    # Main Electron process entry
└── vs/
    └── code/
        ├── electron-main/
        │   ├── main.js                        # Main process startup logic
        │   └── app.js                         # CodeApplication class
        └── electron-browser/
            └── workbench/
                ├── workbench.html             # Workbench HTML template
                └── workbench.js               # Workbench renderer entry
```

## Web Version

### Startup Command
```bash
./scripts/code-web.sh  # Starts web VS Code server
```

### Web Architecture

VS Code web runs in a browser without Electron, using a sophisticated multi-stage bootstrap process:

#### 1. HTML Entry Point and Template System
- **URL**: `http://localhost:8080`
- **Source Templates**:
  - `src/vs/code/browser/workbench/workbench.html` (Production, 49 lines)
  - `src/vs/code/browser/workbench/workbench-dev.html` (Development, 68 lines)
- **Runtime Output**: Complex HTML with embedded JavaScript and configuration
- **Key Difference**: Simple templates with placeholders vs. fully populated runtime HTML

#### 2. Configuration and Asset Loading

```html
<!-- Workbench Configuration -->
<meta id="vscode-workbench-web-configuration" data-settings="...">

<!-- CSS Entry Point -->
<link data-name="vs/workbench/workbench.web.main"
      rel="stylesheet"
      href="http://localhost:8080/static/sources/out/vs/workbench/workbench.web.main.css" />
```

#### 3. Multi-Stage Bootstrap Process

**Stage 1: Base URL Setup**
```javascript
const baseUrl = new URL('http://localhost:8080/static/sources', window.location.origin).toString();
globalThis._VSCODE_FILE_ROOT = baseUrl + '/out/';
```

**Stage 2: Dynamic CSS Module Loading**
- 200+ CSS modules loaded dynamically via import maps
- CSS modules injected via `@import url()` rules
- Blob URLs created for module resolution

**Stage 3: Main Workbench Module Loading**
```javascript
// Final bootstrap - ES Module entry point
import { create, URI, Emitter } from 'http://localhost:8080/static/sources/out/vs/workbench/workbench.web.main.internal.js';
```

#### 4. Web-Specific Providers

**WorkspaceProvider Class**
- Handles workspace management via URL parameters
- Supports folder, workspace, and empty window modes
- Query parameters: `?folder=`, `?workspace=`, `?ew=true`

**LocalStorageURLCallbackProvider Class**
- Manages OAuth/authentication callbacks
- Uses localStorage for cross-tab communication
- Handles URL callback routing

#### 5. Application Initialization

```javascript
// Final initialization call
const config = JSON.parse(configElementAttribute);
create(window.document.body, {
    ...config,
    workspaceProvider: WorkspaceProvider.create(config),
    urlCallbackProvider: new LocalStorageURLCallbackProvider(config.callbackRoute)
});
```

#### 6. Resource Loading Strategy
- **Base URL**: `http://localhost:8080/static/sources`
- **Main Workbench**: `/out/vs/workbench/workbench.web.main.internal.js`
- **CSS Modules**: 200+ individual CSS files loaded via import maps
- **Extensions**: Embedded configuration in HTML meta tags

### Web Bootstrap Flow
```
Browser Request (http://localhost:8080)
    ↓
Dynamically Generated HTML
    ↓
performance.mark('code/didStartRenderer')
    ↓
Base URL Setup (globalThis._VSCODE_FILE_ROOT)
    ↓
CSS Module Import Map Creation (200+ modules)
    ↓
Dynamic CSS Loading via Blob URLs
    ↓
performance.mark('code/willLoadWorkbenchMain')
    ↓
ES Module Import: workbench.web.main.internal.js
    ↓
WorkspaceProvider & URLCallbackProvider Setup
    ↓
create(document.body, config) - Workbench Initialization
```

### Template vs Runtime: A Critical Distinction

**Source Templates (Simple)**
```html
<!-- workbench.html - Clean 49-line template -->
<meta id="vscode-workbench-web-configuration" data-settings="{{WORKBENCH_WEB_CONFIGURATION}}">
<script type="module" src="{{WORKBENCH_WEB_BASE_URL}}/out/vs/code/browser/workbench/workbench.js"></script>

<!-- workbench-dev.html - Development with CSS module system -->
<meta id="vscode-workbench-builtin-extensions" data-settings="{{WORKBENCH_BUILTIN_EXTENSIONS}}">
<script>const cssModules = JSON.parse('{{WORKBENCH_DEV_CSS_MODULES}}');</script>
```

**Runtime Output (Complex)**
When you view source at `http://localhost:8080`, templates are processed into:

1. **Placeholder Replacement**: All `{{VARIABLES}}` filled with actual values
2. **Embedded Configuration**: Complete VS Code configuration in meta tags
3. **Extension Definitions**: All built-in extensions embedded as massive JSON
4. **CSS Module Array**: 200+ CSS module paths in `globalThis._VSCODE_CSS_MODULES`
5. **Bootstrap Classes**: Complete `WorkspaceProvider` and `LocalStorageURLCallbackProvider` implementations
6. **Import Map Generation**: Dynamic blob URL creation for CSS modules
7. **Final Initialization**: Hundreds of lines of embedded JavaScript

### Template Processing Flow
```
Source Template (49-68 lines)
    ↓
Server-Side Processing
    ↓
Placeholder Replacement ({{VARIABLES}} → actual values)
    ↓
Configuration Embedding (JSON data injection)
    ↓
Bootstrap Code Injection (WorkspaceProvider, etc.)
    ↓
Runtime HTML (271+ lines with embedded JavaScript)
    ↓
Browser Receives Fully Populated HTML
```

**Key Insight**: VS Code web uses a sophisticated templating system where simple, clean HTML templates get transformed into complex, self-contained HTML documents with all necessary bootstrap logic embedded. This explains why the view-source shows hundreds of lines of JavaScript that don't exist in the source templates.

## Architecture Comparison

### Desktop (Electron) vs Web

| Aspect | Desktop/Electron | Web |
|--------|------------------|-----|
| **Entry Point** | `out/main.js` | Template-generated HTML (`workbench.html` → complex runtime HTML) |
| **Template System** | None (direct file loading) | Server-side template processing with `{{PLACEHOLDERS}}` |
| **Process Model** | Multi-process (main + renderer) | Single browser process |
| **Runtime** | Node.js + Chromium | Browser JavaScript engine |
| **Bootstrap Strategy** | Sequential file loading | Multi-stage with import maps & blob URLs |
| **CSS Loading** | Static bundled CSS | 200+ dynamic CSS modules via import maps |
| **Main Module** | `workbench.js` (453 lines) | `workbench.web.main.internal.js` (ES module) |
| **File System** | Direct file system access | Web-based file operations |
| **Extensions** | Local extension loading | Embedded in HTML configuration |
| **Authentication** | Native OS integration | LocalStorage + URL callback system |
| **Performance Tracking** | Electron performance APIs | `performance.mark()` web APIs |
| **Workspace Management** | File system paths | URL parameters & WorkspaceProvider |

### Common Elements

Both versions share:
- **Workbench Architecture**: Core UI framework
- **Extension System**: Plugin architecture (with different capabilities)
- **Monaco Editor**: Underlying code editor
- **Service Architecture**: Dependency injection and service management

## Entry Point Analogies

### Desktop Version
Think of the desktop version like a **traditional desktop application**:
- `main.js` = Application launcher (like double-clicking an .exe)
- `workbench.js` = Main window UI (the actual interface you interact with)

### Web Version
Think of the web version like a **web application**:
- HTML page = Application entry point (like visiting a website)
- JavaScript modules = Dynamic loading of application components
- Server = Backend providing resources and APIs

## Development Implications

### For Extension Development
- **Desktop**: Full Node.js API access, file system operations
- **Web**: Browser API limitations, network-based operations

### For Core Development
- **Desktop**: Electron APIs, native integration capabilities
- **Web**: Web standards compliance, cross-browser compatibility

### For Debugging
- **Desktop**:
  - Main process: Node.js debugging tools
  - Renderer process: Chromium DevTools
- **Web**: Standard browser DevTools

## File Path Resolution

### Desktop Version
```
vscode-file://vscode-app/Users/<USER>/github.com/microsoft/vscode/out/vs/code/electron-browser/workbench/workbench.js
```
- Custom protocol (`vscode-file://`)
- Local file system paths
- Direct access to compiled output

### Web Version
```
http://localhost:8080/static/sources/out/vs/workbench/...
```
- HTTP protocol
- Web server served resources
- Network-based loading

## VS Code Release Build Process

VS Code has a sophisticated multi-stage build system that produces optimized release packages for multiple platforms and deployment scenarios. Here's how the release build process works:

### Build Architecture Overview

The release build process involves several key stages:

1. **Compilation** → TypeScript/JavaScript compilation with mangling
2. **Bundling** → Module bundling with tree-shaking and optimization
3. **Minification** → Code minification with source maps
4. **Packaging** → Platform-specific packaging with assets
5. **Code Signing** → Digital signing for security
6. **Distribution** → Publishing to CDN and release channels

### Key Build Commands

#### Main Release Build Scripts
```bash
# Production build with mangling (used for releases)
npm run compile-build                    # gulp compile-build-with-mangling

# Development build without mangling (faster compilation)
npm run compile-build-without-mangling

# Minification tasks
npm run minify-vscode                    # Desktop client minification
npm run minify-vscode-reh               # Remote server minification
npm run minify-vscode-reh-web           # Remote web server minification

# Core CI pipeline (runs all minification in parallel)
npm run core-ci                         # compile + minify all variants
```

#### Platform-Specific Builds
```bash
# Desktop builds for different platforms
gulp vscode-win32-x64                   # Windows 64-bit
gulp vscode-win32-arm64                 # Windows ARM64
gulp vscode-darwin-x64                  # macOS Intel
gulp vscode-darwin-arm64                # macOS Apple Silicon
gulp vscode-linux-x64                   # Linux 64-bit
gulp vscode-linux-arm64                 # Linux ARM64
gulp vscode-linux-armhf                 # Linux ARM (32-bit)

# Server builds
gulp vscode-reh-linux-x64               # Linux server
gulp vscode-reh-web-linux-x64           # Linux web server

# Web build
gulp vscode-web                         # Web version
```

### Build Process Deep Dive

#### 1. Compilation Stage (`gulpfile.compile.js`)

**With Mangling (Release):**
- TypeScript compilation with aggressive optimization
- Variable/function name mangling for size reduction
- Dead code elimination
- Used for production releases

**Without Mangling (Development):**
- Faster compilation for development
- Preserves readable names for debugging
- Used for CI testing and development builds

#### 2. Bundling Stage (`build/lib/optimize.js`)

**ESBuild-Based Bundling:**
```javascript
// Key entry points bundled together
const vscodeEntryPoints = [
  buildfile.workerEditor,           // Monaco editor worker
  buildfile.workerExtensionHost,    // Extension host worker
  buildfile.workerNotebook,         // Notebook worker
  buildfile.workbenchDesktop,       // Main workbench
  buildfile.code                    // Electron main process
];
```

**Bundling Process:**
- **Tree-shaking**: Removes unused code automatically
- **Module resolution**: Resolves all import dependencies
- **Resource inclusion**: Bundles CSS, images, and other assets
- **Source map generation**: Creates debugging source maps
- **Platform targeting**: ES2022 with neutral platform output

#### 3. Minification Stage

**esbuild Minification:**
- JavaScript minification with symbol mangling
- CSS minification for stylesheets
- SVG optimization using `gulp-svgmin`
- External source maps for debugging
- Preserve licensing comments

**Source Map Handling:**
- Maps to CDN: `https://main.vscode-cdn.net/sourcemaps/${commit}/core`
- External source maps for production debugging
- Preserves original TypeScript source information

#### 4. Packaging Stage (`gulpfile.vscode.js`)

**Platform-Specific Packaging:**

```javascript
function packageTask(platform, arch, sourceFolderName, destinationFolderName) {
  // 1. Copy application files with checksums
  const checksums = computeChecksums(out, [
    'vs/workbench/workbench.desktop.main.js',
    'vs/workbench/workbench.desktop.main.css',
    'vs/code/electron-browser/workbench/workbench.html',
    'vs/code/electron-browser/workbench/workbench.js'
  ]);

  // 2. Bundle with Electron runtime
  // 3. Add platform-specific resources
  // 4. Configure application metadata
}
```

**Windows Packaging:**
- Executable signing and metadata
- Visual Elements Manifest for Start Menu tiles
- Shell integration scripts (`code.cmd`, `code.sh`)
- Policy files for enterprise deployment
- Inno Setup installer generation

**macOS Packaging:**
- Apple code signing and notarization
- Application bundle structure
- Universal binary support (Intel + ARM)
- DMG installer creation

**Linux Packaging:**
- AppImage, Snap, and DEB/RPM packages
- Desktop integration files
- Shell completion scripts
- Repository metadata

#### 5. Azure Pipelines CI/CD (`build/azure-pipelines/`)

**Build Pipeline Structure:**
```yaml
# product-build.yml - Main build orchestration
stages:
  - Compile          # TypeScript compilation
  - Test            # Unit and integration tests
  - Build           # Platform-specific builds
  - Package         # Create distribution packages
  - Publish         # Upload to CDN and release channels
  - Release         # Final release approval and distribution
```

**Platform Build Matrix:**
- **Windows**: x64, ARM64 with code signing
- **macOS**: x64, ARM64, Universal with notarization
- **Linux**: x64, ARM64, ARMhf with multiple package formats
- **Web**: Browser-optimized build for vscode.dev

### Build Output Structure

#### Desktop Application (`out-vscode/`)
```
out-vscode/
├── main.js                    # Electron main process entry
├── vs/
│   ├── workbench/
│   │   ├── workbench.desktop.main.js    # Main workbench bundle
│   │   └── workbench.desktop.main.css   # Workbench styles
│   └── code/electron-browser/workbench/
│       ├── workbench.html     # Workbench HTML template
│       └── workbench.js       # Workbench initialization
└── [resources, workers, etc.]
```

#### Server Application (`out-vscode-reh/`)
```
out-vscode-reh/
├── server-main.js             # Server entry point
├── vs/server/               # Server-specific code
└── [shared workbench code]    # Common workbench components
```

#### Web Application (`out-vscode-web/`)
```
out-vscode-web/
├── vs/workbench/
│   └── workbench.web.main.js  # Web workbench bundle
└── [web-optimized resources]
```

### Build Variants and Optimization

#### Development vs Production
- **Development**: No mangling, faster compilation, readable code
- **Production**: Full mangling, minification, optimized for size/performance

#### Client vs Server vs Web
- **Client**: Full Electron app with native APIs
- **Server**: Headless server for remote development
- **Web**: Browser-optimized for web deployment

#### Architecture Targets
- **x64**: Standard 64-bit Intel/AMD processors
- **ARM64**: Apple Silicon, ARM-based servers
- **ARMhf**: 32-bit ARM devices (Raspberry Pi)
- **Universal**: Combined Intel + ARM binaries (macOS)

### Quality Levels

VS Code supports different quality levels with distinct build characteristics:

- **`stable`**: Production releases with full testing
- **`insider`**: Daily builds with latest features
- **`exploration`**: Experimental builds for testing
- **`oss`**: Open source builds without Microsoft branding

### Release Process

#### 1. Automated Build Triggers
- **Scheduled**: Daily builds for insider channel
- **Manual**: Triggered releases for stable versions
- **Pull Request**: Build validation for contributions

#### 2. Build Validation
- **Hygiene checks**: Code style and quality validation
- **Unit tests**: Core functionality testing
- **Integration tests**: End-to-end scenario testing
- **Smoke tests**: Basic functionality validation

#### 3. Code Signing and Security
- **Windows**: Authenticode signing with ESRP
- **macOS**: Apple Developer ID signing and notarization
- **Linux**: GPG signing for package repositories

#### 4. Distribution
- **CDN Upload**: Assets uploaded to `main.vscode-cdn.net`
- **Package Stores**: Microsoft Store, Mac App Store, Snap Store
- **Direct Download**: GitHub releases and official website
- **Auto-update**: In-app update system for existing installations

### Performance Optimizations

#### Bundle Size Optimization
- **Tree-shaking**: Removes unused code automatically
- **Code splitting**: Separates frequently updated code
- **Lazy loading**: Defers loading of optional features
- **Asset optimization**: Compresses images and media files

#### Runtime Performance
- **V8 Optimization**: Leverages Chrome's JavaScript engine optimizations
- **Web Workers**: Offloads heavy computation to background threads
- **Caching**: Aggressive caching of compiled modules and assets
- **Startup Optimization**: Minimizes initial loading time

This sophisticated build system enables VS Code to deliver consistent, high-performance applications across all supported platforms while maintaining a single codebase for the core editor functionality.

## Summary

VS Code's architecture demonstrates sophisticated entry point management with fundamentally different strategies:

### Desktop (Electron)
- **Traditional Process Model**: Main process (`main.js`) → Renderer process (`workbench.js`)
- **File-based Loading**: Sequential loading of compiled JavaScript files
- **Simple Bootstrap**: Direct file system access and standard Electron APIs

### Web
- **Embedded Bootstrap**: Complete application logic embedded in dynamically generated HTML
- **Import Map Strategy**: 200+ CSS modules loaded via blob URLs and import maps
- **Web-Native Providers**: Custom workspace and authentication providers for browser environment
- **Performance Optimized**: Multi-stage loading with performance markers

### Release Build System
- **Multi-stage Pipeline**: Compilation → Bundling → Minification → Packaging → Distribution
- **Platform Matrix**: Windows, macOS, Linux across x64, ARM64, and ARMhf architectures
- **Quality Levels**: Stable, Insider, Exploration, and OSS variants
- **Performance Optimization**: Tree-shaking, code splitting, and aggressive caching

### Key Architectural Differences

1. **Desktop loads files sequentially**; **Web uses server-side templating to embed everything**
2. **Desktop loads static files**; **Web transforms 49-line templates into 271+ line runtime HTML**
3. **Desktop uses Electron IPC**; **Web uses localStorage and URL parameters**
4. **Desktop has 453-line workbench.js**; **Web loads workbench.js via template placeholders**
5. **Desktop uses bundled CSS**; **Web dynamically imports 200+ CSS modules via template injection**

### Shared Core
Both platforms ultimately load the same VS Code workbench architecture but through completely different entry point strategies optimized for their respective environments.

The `out/` directory contains the compiled application logic, but desktop loads it via traditional file system access while web serves it through a sophisticated templating system that transforms simple HTML templates into self-contained documents with embedded bootstrap logic.

---

## VS Code Build Output Directories

VS Code uses a sophisticated multi-stage build system that produces four different output directories, each optimized for specific use cases in the development and production pipeline.

### Overview

| Directory | Purpose | Command | Stage | CSS State | JS State |
|-----------|---------|---------|-------|-----------|----------|
| `out/` | Development | `npm run compile` | Development | Individual files | Development-optimized |
| `out-build/` | Build prep | `npm run gulp compile-build-*` | Pre-bundling | Individual files | Bundle-optimized |
| `out-vscode/` | Bundled | `npm run gulp bundle-vscode` | Pre-production | Single bundled file | Bundled modules |
| `out-vscode-min/` | Production | `npm run gulp minify-vscode` | Production | Minified bundle | Minified bundles |

### Build Pipeline Flow

```
src/ (TypeScript Source)
    ↓
    ├── npm run compile → out/ (Development)
    └── npm run gulp compile-build-* → out-build/ (Build-Optimized)
                                          ↓
                                      npm run gulp bundle-vscode → out-vscode/ (Bundled)
                                                                      ↓
                                                                  npm run gulp minify-vscode → out-vscode-min/ (Production)
```

---

## 1. `out/` - Development Build Output

### Purpose
The `out/` directory contains the **development build** optimized for fast compilation and debugging during active development.

### Generation Command
```bash
npm run compile
# Equivalent to: node ./node_modules/gulp/bin/gulp.js compile
```

### File Structure
```
out/
├── vs/
│   ├── base/browser/ui/aria/aria.css          # Individual CSS files
│   ├── editor/browser/widget/codeEditor/editor.css
│   ├── workbench/
│   │   ├── workbench.desktop.main.js         # Compiled TypeScript
│   │   ├── contrib/comments/browser/media/review.css
│   │   └── services/themes/browser/workbenchThemeService.js
│   └── code/electron-browser/workbench/
│       ├── workbench.html                    # HTML templates
│       └── workbench.js                      # Bootstrap scripts
├── cli.js                                    # CLI entry point
└── main.js                                   # Main process entry
```

### CSS Handling
- **Individual CSS files**: Each component keeps its own CSS file (~200+ files)
- **No bundling**: CSS files remain separate for modularity
- **Dynamic loading**: CSS loaded via `_VSCODE_CSS_LOAD` function at runtime
- **Development logs**: `[CSS_DEV] DONE, 265 css modules (334ms)`

### JavaScript Processing
- **TypeScript compilation**: Direct TS → JS compilation
- **No bundling**: Modules remain separate
- **ES modules**: Uses ES6 import/export syntax
- **Source maps**: Inline source maps for debugging

### Use Cases
- **Active development**: Fast incremental compilation (~2 minutes)
- **Debugging**: Easy to trace issues to specific files
- **Hot reloading**: Individual file changes don't require full rebuild
- **Extension development**: Running VS Code from source with `./scripts/code.sh`

---

## 2. `out-build/` - Build-Optimized Compilation Output

### Purpose
The `out-build/` directory contains **build-optimized TypeScript compilation** that serves as the source for bundling operations. It's an intermediate step between development compilation and final bundling.

### Generation Command
```bash
npm run gulp compile-build-without-mangling
# or
npm run gulp compile-build-with-mangling
```

### Key Differences from `out/`

| Aspect | `out/` (Development) | `out-build/` (Build-Optimized) |
|--------|---------------------|--------------------------------|
| **Purpose** | Development/debugging | Pre-bundling optimization |
| **TypeScript Config** | `src/tsconfig.json` | `build/tsconfig.build.json` |
| **Optimization** | Minimal | Build-optimized |
| **Module Resolution** | Development-friendly | Bundle-ready |

### CSS Handling
- **Individual CSS files**: Same as `out/` - no bundling yet
- **Build optimization**: CSS prepared for bundling
- **Source preparation**: CSS ready for ESBuild processing

### JavaScript Processing
- **Build-optimized compilation**: More aggressive TypeScript compilation
- **Bundle preparation**: Code structured for efficient bundling
- **Import optimization**: Module imports optimized for bundling

### Use Cases
- **Bundling source**: ESBuild input for creating bundles
- **Intermediate step**: Rarely used directly by developers
- **CI/CD pipelines**: Preparation step for production builds

---

## 3. `out-vscode/` - Bundled Build Output

### Purpose
The `out-vscode/` directory contains the **bundled build** where related modules are combined for better performance while maintaining readability.

### Generation Command
```bash
# First compile for build (creates out-build/)
npm run gulp compile-build-without-mangling

# Then bundle (uses out-build/ as source)
npm run gulp bundle-vscode
```

### File Structure
```
out-vscode/
├── vs/
│   ├── workbench/
│   │   ├── workbench.desktop.main.css        # 🎯 BUNDLED CSS (883KB, 25,926 lines)
│   │   ├── workbench.desktop.main.css.map    # CSS source map
│   │   ├── workbench.desktop.main.js         # Bundled JavaScript
│   │   └── workbench.desktop.main.js.map     # JS source map
│   ├── editor/common/services/
│   │   └── editorWebWorkerMain.js            # Worker bundles
│   └── code/electron-browser/workbench/
│       ├── workbench.html
│       └── workbench.js
├── cli.js
└── main.js
```

### CSS Handling
- **Single bundled file**: `workbench.desktop.main.css` combines 249 individual CSS files
- **Source comments**: Each section marked with original file path
- **Example**:
  ```css
  /* out-build/vs/base/browser/ui/aria/aria.css */
  .monaco-aria-container {
    position: absolute;
    left: -999em;
  }

  /* out-build/vs/editor/browser/widget/codeEditor/editor.css */
  .monaco-editor {
    position: relative;
    color: var(--vscode-editor-foreground);
  }
  ```

### JavaScript Processing
- **ESBuild bundling**: Related modules combined using ESBuild
- **Tree shaking**: Unused code eliminated
- **External dependencies**: Node modules kept external
- **ES modules**: Maintains ES6 module format

### Use Cases
- **Pre-production testing**: Testing bundled performance
- **CI/CD pipelines**: Intermediate build for testing
- **Performance analysis**: Measuring bundle impact
- **Distribution preparation**: Step before final minification

### Source Maps
- **External source maps**: Separate `.map` files
- **Bundle-aware mapping**: Maps bundled code back to original sources
- **Debugging support**: Full debugging capabilities maintained

---

## 4. `out-vscode-min/` - Minified Production Build

### Purpose
The `out-vscode-min/` directory contains the **production-ready build** with maximum optimization for deployment.

### Generation Command
```bash
# Runs bundle-vscode first, then minifies
npm run gulp minify-vscode
```

### File Structure
```
out-vscode-min/
├── vs/
│   ├── workbench/
│   │   ├── workbench.desktop.main.css        # 🎯 MINIFIED CSS (~400KB)
│   │   ├── workbench.desktop.main.css.map    # Minified CSS source map
│   │   ├── workbench.desktop.main.js         # Minified JavaScript
│   │   └── workbench.desktop.main.js.map     # Minified JS source map
│   └── [other minified bundles]
├── cli.js                                    # Minified CLI
└── main.js                                   # Minified main process
```

### CSS Handling
- **Minified CSS**: Whitespace removed, properties optimized
- **Compressed**: ~50% size reduction from bundled version
- **Example**:
  ```css
  .monaco-aria-container{position:absolute;left:-999em}.monaco-editor{position:relative;color:var(--vscode-editor-foreground)}
  ```

### JavaScript Processing
- **ESBuild minification**: Aggressive minification
- **Variable mangling**: Shortened variable names
- **Dead code elimination**: Maximum tree shaking
- **Compression**: Optimal file sizes

### Use Cases
- **Production deployment**: Final build for distribution
- **Official releases**: VS Code stable/insiders builds
- **Performance critical**: Maximum loading speed required
- **Bandwidth constrained**: Mobile or slow connections

### Source Maps
- **Production source maps**: Optimized for debugging minified code
- **CDN compatible**: Can be hosted separately
- **Debugging support**: Full source reconstruction possible
