# Built-in Extensions Bundling Documentation

## Overview

This document provides comprehensive details about how built-in extensions are bundled into the build/release process in the VS Code codebase. The system handles both **local extensions** (developed in-tree) and **marketplace extensions** (downloaded from external sources) through a sophisticated build pipeline.

## 🚀 **Extension Bundling Philosophy**

**Two-Tiered Extension System**:

- **📦 Local Extensions**: Developed in-tree in the `extensions/` directory
- **🌐 Marketplace Extensions**: Downloaded from external repositories/marketplace
- **🔄 Build-Time Integration**: Extensions are compiled and bundled during the build process
- **🎯 Platform-Specific Optimization**: Web vs. desktop targeting with appropriate filtering
- **🛠️ Automatic Dependency Management**: Production dependencies are automatically included

```typescript
// Extension bundling happens at build time:
const builtInExtensions = productJson.builtInExtensions || [];
const webBuiltInExtensions = productJson.webBuiltInExtensions || [];
// ✅ Extensions configured in product.json
// ✅ Local extensions auto-discovered
// ✅ Marketplace extensions downloaded
// ✅ Platform-specific filtering applied
// ✅ Ready for distribution
```

## Architecture

### **Extension Build Pipeline**

```
🏗️ Extension Build System
├─ Configuration Layer
│   ├─ product.json (extension definitions)
│   ├─ builtInExtensions[] (marketplace extensions)
│   └─ webBuiltInExtensions[] (web-specific extensions)
├─ Discovery Layer
│   ├─ Local extension scanning (extensions/*/package.json)
│   ├─ Marketplace extension downloading
│   └─ Platform-specific filtering
├─ Compilation Layer
│   ├─ TypeScript compilation (gulpfile.extensions.js)
│   ├─ Resource minification
│   └─ Bundle generation
└─ Distribution Layer
    ├─ .build/extensions/ (bundled output)
    ├─ Web-specific bundles
    └─ Native extension bundles
```

### **Build Process Flow**

```
📡 Build Request
    ↓
🔍 Extension Discovery
    ├─ Scan extensions/ directory
    ├─ Read product.json configuration
    └─ Download marketplace extensions
    ↓
🛠️ Compilation & Bundling
    ├─ TypeScript compilation
    ├─ Resource minification
    ├─ Platform-specific filtering
    └─ Dependency inclusion
    ↓
📦 Bundle Generation
    ├─ Local extensions → .build/extensions/
    ├─ Marketplace extensions → .build/extensions/
    └─ Web-specific bundles
    ↓
🚀 Distribution Ready
    ├─ Extensions bundled in build
    ├─ Runtime loading configured
    └─ Platform-specific optimizations
```

## Extension Types & Configuration

### **1. Local Extensions (In-Tree)**

**Developed within the vscode_ts/extensions/ directory**

- 🎯 **Location**: `vscode_ts/extensions/*/`
- 🔧 **Discovery**: Automatic scanning of `extensions/*/package.json`
- 📦 **Compilation**: TypeScript compilation with source maps
- 🚀 **Integration**: Direct bundling into `.build/extensions/`

**Discovery Process**:
```typescript
// Auto-discovery of local extensions
const localExtensionsDescriptions = (
    glob.sync('extensions/*/package.json')
        .map(manifestPath => {
            const absoluteManifestPath = path.join(root, manifestPath);
            const extensionPath = path.dirname(path.join(root, manifestPath));
            const extensionName = path.basename(extensionPath);
            return { name: extensionName, path: extensionPath, manifestPath: absoluteManifestPath };
        })
        .filter(({ name }) => excludedExtensions.indexOf(name) === -1)
        .filter(({ name }) => builtInExtensions.every(b => b.name !== name))
        .filter(({ manifestPath }) => (forWeb ? isWebExtension(require(manifestPath)) : true))
);
```

**Examples of Local Extensions**:
- `typescript-language-features/` - TypeScript language support
- `json-language-features/` - JSON language support
- `markdown-language-features/` - Markdown language support
- `theme-*` - Built-in themes
- `git/` - Git integration
- `emmet/` - Emmet support

### **2. Marketplace Extensions (External)**

**Downloaded from external repositories**

- 🎯 **Configuration**: Defined in `product.json`
- 🔧 **Download**: Automatic during build process
- 📦 **Caching**: Cached in `.build/builtInExtensions/`
- 🚀 **Integration**: Same bundling process as local extensions

**Configuration in product.json**:
```json
{
  "builtInExtensions": [
    {
      "name": "ms-vscode.js-debug-companion",
      "version": "1.1.3",
      "sha256": "7380a890787452f14b2db7835dfa94de538caf358ebc263f9d46dd68ac52de93",
      "repo": "https://github.com/microsoft/vscode-js-debug-companion",
      "metadata": {
        "id": "99cb0b7f-7354-4278-b8da-6cc79972169d",
        "publisherId": {
          "publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee",
          "publisherName": "ms-vscode",
          "displayName": "Microsoft",
          "flags": "verified"
        },
        "publisherDisplayName": "Microsoft"
      }
    }
  ]
}
```

**Download & Caching Process**:
```typescript
// Marketplace extension download and caching
export function getBuiltInExtensions(): Promise<void> {
    log('Synchronizing built-in extensions...');
    
    const control = readControlFile();
    const streams: Stream[] = [];
    
    for (const extension of [...builtInExtensions, ...webBuiltInExtensions]) {
        const controlState = control[extension.name] || 'marketplace';
        control[extension.name] = controlState;
        
        streams.push(syncExtension(extension, controlState));
    }
    
    return new Promise((resolve, reject) => {
        es.merge(streams)
            .on('error', reject)
            .on('end', resolve);
    });
}
```

### **3. Web Extensions (Browser-Compatible)**

**Extensions optimized for web/browser environments**

- 🎯 **Target**: Web-based VS Code instances
- 🔧 **Filtering**: Automatic filtering based on extension capabilities
- 📦 **Configuration**: Separate `webBuiltInExtensions` array
- 🚀 **Optimization**: No native dependencies, browser-compatible code

**Web Extension Detection**:
```typescript
function isWebExtension(manifest: any): boolean {
    if (Boolean(manifest.browser)) {
        return true;
    }
    if (Boolean(manifest.main)) {
        return false;
    }
    // Check extensionKind
    if (typeof manifest.extensionKind !== 'undefined') {
        const extensionKind = Array.isArray(manifest.extensionKind) 
            ? manifest.extensionKind 
            : [manifest.extensionKind];
        if (extensionKind.indexOf('web') >= 0) {
            return true;
        }
    }
    // Check for non-web contributions
    if (typeof manifest.contributes !== 'undefined') {
        for (const id of ['debuggers', 'terminal', 'typescriptServerPlugins']) {
            if (manifest.contributes.hasOwnProperty(id)) {
                return false;
            }
        }
    }
    return true;
}
```

## Build System Integration

### **Gulp Task Structure**

**Extension-related build tasks are organized in `gulpfile.extensions.js`**:

```typescript
// Main compilation tasks
const compileExtensionsTask = task.define('compile-extensions', 
    task.parallel(...tasks.map(t => t.compileTask)));

// Build-specific tasks
const compileAllExtensionsBuildTask = task.define('compile-extensions-build', 
    task.series(
        cleanExtensionsBuildTask,
        bundleMarketplaceExtensionsBuildTask,
        task.define('bundle-extensions-build', 
            () => ext.packageAllLocalExtensionsStream(false, false).pipe(gulp.dest('.build'))
        )
    ));

// Web-specific tasks
const compileWebExtensionsBuildTask = task.define('compile-web-extensions-build', 
    task.series(
        task.define('clean-web-extensions-build', util.rimraf('.build/web/extensions')),
        task.define('bundle-web-extensions-build', 
            () => extensions.packageAllLocalExtensionsStream(true, false).pipe(gulp.dest('.build/web'))
        ),
        task.define('bundle-marketplace-web-extensions-build', 
            () => extensions.packageMarketplaceExtensionsStream(true).pipe(gulp.dest('.build/web'))
        )
    ));
```

### **Extension Compilation Process**

**TypeScript Compilation with Source Maps**:

```typescript
// Per-extension compilation pipeline
function createPipeline(build: boolean, emitError: boolean, transpileOnly?: boolean) {
    const compilation = tsb.create(absolutePath, overrideOptions, {
        verbose: false,
        transpileOnly,
        transpileOnlyIncludesDts: transpileOnly,
        transpileWithEsbuild: true
    });
    
    return function pipeline() {
        const tsFilter = filter(['**/*.ts', '!**/lib/lib*.d.ts', '!**/node_modules/**']);
        
        return input
            .pipe(tsFilter)
            .pipe(util.loadSourcemaps())
            .pipe(compilation())
            .pipe(sourcemaps.write('.', {
                sourceMappingURL: !build ? null : f => `${baseUrl}/${f.relative}.map`,
                addComment: !!build,
                includeContent: !!build,
                sourceRoot: '../src/',
            }))
            .pipe(tsFilter.restore);
    };
}
```

### **Resource Minification**

**JSON and Configuration File Optimization**:

```typescript
function minifyExtensionResources(input: Stream): Stream {
    const jsonFilter = filter(['**/*.json', '**/*.code-snippets'], { restore: true });
    return input
        .pipe(jsonFilter)
        .pipe(buffer())
        .pipe(es.mapSync((f: File) => {
            const errors: jsoncParser.ParseError[] = [];
            const value = jsoncParser.parse(f.contents.toString('utf8'), errors, {
                allowTrailingComma: true
            });
            if (errors.length === 0) {
                // Drop whitespace and comments
                f.contents = Buffer.from(JSON.stringify(value));
            }
            return f;
        }))
        .pipe(jsonFilter.restore);
}
```

## Runtime Integration

### **Extension Loading at Runtime**

**Built-in Extensions Scanner Service**:

```typescript
// Browser-based extension scanning
export class BuiltinExtensionsScannerService implements IBuiltinExtensionsScannerService {
    constructor(/* dependencies */) {
        if (builtinExtensionsServiceUrl) {
            let bundledExtensions: IBundledExtension[] = [];
            
            if (environmentService.isBuilt) {
                // Built time configuration (do NOT modify)
                bundledExtensions = [/*BUILD->INSERT_BUILTIN_EXTENSIONS*/];
            } else {
                // Development: Find extensions from DOM
                const builtinExtensionsElement = mainWindow.document
                    .getElementById('vscode-workbench-builtin-extensions');
                const builtinExtensionsElementAttribute = builtinExtensionsElement 
                    ? builtinExtensionsElement.getAttribute('data-settings') 
                    : undefined;
                if (builtinExtensionsElementAttribute) {
                    bundledExtensions = JSON.parse(builtinExtensionsElementAttribute);
                }
            }
            
            // Create extension promises
            this.builtinExtensionsPromises = bundledExtensions.map(async e => {
                const id = getGalleryExtensionId(e.packageJSON.publisher, e.packageJSON.name);
                return {
                    identifier: { id },
                    location: uriIdentityService.extUri.joinPath(
                        builtinExtensionsServiceUrl, 
                        e.extensionPath
                    ),
                    type: ExtensionType.System,
                    isBuiltin: true,
                    manifest: e.packageNLS 
                        ? await this.localizeManifest(id, e.packageJSON, e.packageNLS) 
                        : e.packageJSON,
                    targetPlatform: TargetPlatform.WEB,
                    isValid: true,
                    preRelease: false,
                };
            });
        }
    }
}
```

### **Build-Time Extension Injection**

**Extensions are injected into the build at compile time**:

```typescript
// Build-time placeholder replacement
if (environmentService.isBuilt) {
    // Built time configuration (do NOT modify)
    bundledExtensions = [/*BUILD->INSERT_BUILTIN_EXTENSIONS*/];
}
```

The `/*BUILD->INSERT_BUILTIN_EXTENSIONS*/` placeholder is replaced during the build process with the actual bundled extension metadata.

## Platform-Specific Considerations

### **Native Extensions**

**Extensions with native dependencies**:

```typescript
// Extensions requiring native compilation
const nativeExtensions = [
    'microsoft-authentication',
];

// Platform-specific filtering
const localExtensionsDescriptions = (
    glob.sync('extensions/*/package.json')
        .filter(({ name }) => native 
            ? nativeExtensionsSet.has(name) 
            : !nativeExtensionsSet.has(name)
        )
        // ... other filters
);
```

### **Web-Specific Exclusions**

**Extensions that cannot run in web environments**:

```typescript
const marketplaceWebExtensionsExclude = new Set([
    'ms-vscode.node-debug',
    'ms-vscode.node-debug2',
    'ms-vscode.js-debug-companion',
    'ms-vscode.js-debug',
    'ms-vscode.vscode-js-profile-table'
]);
```

## CI/CD Integration

### **Azure Pipelines Integration**

**Built-in extensions are handled in the CI pipeline**:

```yaml
# install-builtin-extensions.yml
steps:
  - script: node build/azure-pipelines/common/computeBuiltInDepsCacheKey.js > .build/builtindepshash
    displayName: Prepare built-in extensions cache key

  - task: Cache@2
    inputs:
      key: '"builtin-extensions" | .build/builtindepshash'
      path: .build/builtInExtensions
      cacheHitVar: BUILTIN_EXTENSIONS_RESTORED
    displayName: Restore built-in extensions cache

  - script: node build/lib/builtInExtensions.js
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    condition: and(succeeded(), ne(variables.BUILTIN_EXTENSIONS_RESTORED, 'true'))
    displayName: Download built-in extensions
```

### **Quality-Specific Extension Mixins**

**Different builds can have different extension sets**:

```typescript
// mixin-quality.ts - Quality-specific extension handling
if (Array.isArray(distro.builtInExtensions)) {
    log('Overwriting built-in extensions:', distro.builtInExtensions.map(e => e.name));
    builtInExtensions = distro.builtInExtensions;
} else if (distro.builtInExtensions) {
    const include = distro.builtInExtensions['include'] ?? [];
    const exclude = distro.builtInExtensions['exclude'] ?? [];
    
    builtInExtensions = builtInExtensions.filter(ext => 
        !include.find(e => e.name === ext.name) && 
        !exclude.find(name => name === ext.name)
    );
    builtInExtensions = [...builtInExtensions, ...include];
}
```

## Development Workflow

### **Pre-Launch Process**

**Extensions are prepared before VS Code starts**:

```typescript
// preLaunch.ts
async function main() {
    await ensureNodeModules();
    await getElectron();
    await ensureCompiled();
    
    // Download and prepare built-in extensions
    const { getBuiltInExtensions } = require('./builtInExtensions');
    await getBuiltInExtensions();
}
```

### **Extension Management Commands**

**Built-in extension management**:

```bash
# Windows batch script
if "%~1"=="--builtin" goto builtin
# ... other setup
:builtin
%CODE% build/builtin
```

**Extension control file**:
```typescript
// Control file for managing extension sources
const controlFilePath = path.join(os.homedir(), '.vscode-oss-dev', 'extensions', 'control.json');

// Extension state management
const controlState = control[extension.name] || 'marketplace';
control[extension.name] = controlState;
```

## Build Output Structure

### **Extension Build vs Main VS Code Build**

**Extensions have their own separate build pipeline and do NOT go into the main VS Code `out/` directories.**

| **Extension Type** | **Build Location** | **Purpose** |
|-------------------|-------------------|-------------|
| **Individual Extension Development** | `extensions/[name]/out/` | Each extension's compiled TypeScript |
| **Bundled Extensions (Desktop)** | `.build/extensions/` | Final bundled extensions for distribution |
| **Web Extensions** | `.build/web/extensions/` | Browser-compatible extension bundles |
| **Downloaded Marketplace Extensions** | `.build/builtInExtensions/` | External extensions cache |

### **Main VS Code Build Locations** (Extensions NOT included)

| **Directory** | **Purpose** | **Contains Extensions?** |
|---------------|-------------|------------------------|
| `out/` | Development | ❌ **No** - Main VS Code code only |
| `out-build/` | Build prep | ❌ **No** - Main VS Code code only |
| `out-vscode/` | Bundled | ❌ **No** - Main VS Code code only |
| `out-vscode-min/` | Production | ❌ **No** - Main VS Code code only |

### **Build Pipeline Separation**

```
VS Code Build Pipeline:
src/ → out/ → out-build/ → out-vscode/ → out-vscode-min/
     (Main VS Code application code)

Extension Build Pipeline:
extensions/[name]/src/ → extensions/[name]/out/ → .build/extensions/[name]/
                        (Individual extension compilation)  (Bundled for distribution)
```

### **Key Build Commands**

**Extensions Only:**
```bash
# Compile all extensions (development)
npm run compile-extensions

# Build extensions for distribution
npm run gulp compile-extensions-build

# Build web extensions
npm run gulp compile-web-extensions-build
```

**Full Build (VS Code + Extensions):**
```bash
# Complete build including extensions
npm run compile
# This runs both main VS Code compilation AND extension compilation
```

## Directory Structure

```
vscode_ts/
├── extensions/                          # Local extensions (in-tree)
│   ├── typescript-language-features/   # TypeScript support
│   │   ├── src/                        # Extension source code
│   │   └── out/                        # ✅ Individual extension compiled output
│   ├── json-language-features/         # JSON support
│   │   ├── src/                        # Extension source code
│   │   └── out/                        # ✅ Individual extension compiled output
│   ├── markdown-language-features/     # Markdown support
│   ├── git/                            # Git integration
│   ├── emmet/                          # Emmet support
│   └── theme-*/                        # Built-in themes
├── src/                                # Main VS Code source
├── out/                                # ❌ Main VS Code compiled output (NO extensions)
├── out-build/                          # ❌ Main VS Code build prep (NO extensions)
├── out-vscode/                         # ❌ Main VS Code bundled (NO extensions)
├── out-vscode-min/                     # ❌ Main VS Code production (NO extensions)
├── .build/
│   ├── extensions/                     # ✅ Bundled extensions for distribution
│   ├── builtInExtensions/              # ✅ Downloaded marketplace extensions
│   └── web/extensions/                 # ✅ Web-specific extension bundles
├── build/
│   ├── lib/
│   │   ├── extensions.ts               # Extension bundling logic
│   │   ├── builtInExtensions.ts        # Marketplace extension management
│   │   └── builtInExtensionsCG.ts      # Code generation utilities
│   ├── gulpfile.extensions.js          # Extension build tasks
│   └── azure-pipelines/                # CI/CD integration
└── product.json                        # Extension configuration
```

### **Final Distribution Structure**

In the final VS Code distribution, extensions end up in:
- **Desktop:** `resources/app/extensions/`
- **Web:** Bundled into the web application
- **Electron:** Packaged with the main application

## Best Practices

### **Adding New Local Extensions**

1. **Create Extension Directory**: Create `extensions/my-extension/`
2. **Add package.json**: Define extension metadata and dependencies
3. **Implement Extension**: Add TypeScript source files in `src/`
4. **Test Locally**: Extension will be auto-discovered and built
5. **Update Configuration**: Add to exclusion lists if needed

### **Adding New Marketplace Extensions**

1. **Update product.json**: Add extension definition with version and hash
2. **Test Download**: Run `node build/lib/builtInExtensions.js`
3. **Verify Bundle**: Check `.build/builtInExtensions/`
4. **Update CI**: Ensure caching and download work correctly

### **Web Extension Considerations**

1. **Check Compatibility**: Ensure no native dependencies
2. **Test in Browser**: Verify extension works in web environment
3. **Update Filters**: Add to web extension lists if needed
4. **Optimize Bundle**: Consider web-specific optimizations

## Troubleshooting

### **Common Issues**

1. **Extension Not Found**: Check extension discovery filters
2. **Build Failures**: Verify TypeScript compilation configuration
3. **Web Incompatibility**: Review native dependency usage
4. **Cache Issues**: Clear `.build/builtInExtensions/` cache
5. **Download Failures**: Check GitHub token and network access

### **Debug Commands**

```bash
# Clean extension build cache
rm -rf .build/extensions/

# Rebuild specific extension
gulp compile-extension:typescript-language-features

# Download fresh marketplace extensions
rm -rf .build/builtInExtensions/
node build/lib/builtInExtensions.js

# Build web extensions
gulp compile-web-extensions-build
```

## Conclusion

The built-in extension bundling system provides a sophisticated, automated approach to integrating both local and marketplace extensions into VS Code builds. The system handles:

- **Automatic Discovery**: Local extensions are automatically discovered and built
- **Marketplace Integration**: External extensions are downloaded and cached
- **Platform Optimization**: Web vs. desktop targeting with appropriate filtering
- **Build Integration**: Seamless integration with the overall build pipeline
- **Runtime Loading**: Efficient loading and initialization at runtime

This architecture ensures that extensions are properly bundled, optimized, and available in the final VS Code distribution while maintaining flexibility for different deployment scenarios. 