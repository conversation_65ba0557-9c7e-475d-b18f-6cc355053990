package main

import (
	"embed"
	"io"
	"io/fs"
	"log"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"github.com/yudaprama/kawai-agent/internal"
)

// Wails uses <PERSON>'s `embed` package to embed the frontend files into the binary.
// Any files in the frontend/dist folder will be embedded into the binary and
// made available to the frontend.
// See https://pkg.go.dev/embed for more information.

//go:embed all:frontend
var assets embed.FS

//go:embed all:vscode_ts/out
var vscodeAssets embed.FS

// createAssetHandler creates a custom asset handler that serves both frontend and VS Code assets
func createAssetHandler() http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		path := strings.TrimPrefix(r.URL.Path, "/")

		// Handle VS Code assets (serve from vscode_ts/out/)
		if strings.HasPrefix(path, "vscode/") {
			vscodeAssetPath := strings.TrimPrefix(path, "vscode/")
			if vscodeAssetPath == "" {
				vscodeAssetPath = "index.html" // Default to index for VS Code
			}

			// Try to serve from VS Code assets
			if file, err := vscodeAssets.Open("vscode_ts/out/" + vscodeAssetPath); err == nil {
				defer file.Close()
				if stat, err := file.Stat(); err == nil && !stat.IsDir() {
					// Set appropriate content type
					if strings.HasSuffix(vscodeAssetPath, ".js") {
						w.Header().Set("Content-Type", "application/javascript")
					} else if strings.HasSuffix(vscodeAssetPath, ".css") {
						w.Header().Set("Content-Type", "text/css")
					} else if strings.HasSuffix(vscodeAssetPath, ".html") {
						w.Header().Set("Content-Type", "text/html")
					}

					http.ServeContent(w, r, vscodeAssetPath, stat.ModTime(), file.(io.ReadSeeker))
					return
				}
			}
		}

		// Default to serving regular frontend assets
		frontendFS, _ := fs.Sub(assets, "frontend")
		http.FileServer(http.FS(frontendFS)).ServeHTTP(w, r)
	})
}

// main function serves as the application's entry point. It initializes the application, creates a window,
// and starts a goroutine that emits a time-based event every second. It subsequently runs the application and
// logs any error that might occur.
func main() {
	// Initialize direct VS Code service (from vscode_go/main.go)
	vsCodeService, err := internal.NewVSCodeService()
	if err != nil {
		log.Fatalf("Failed to initialize VS Code service: %v", err)
	}
	defer vsCodeService.Shutdown()

	app := application.New(application.Options{
		Name:        "Kawai Chat with VS Code",
		Description: "A beautiful translucent chat application with integrated VS Code functionality",
		Services:    []application.Service{application.NewService(vsCodeService)},
		LogLevel:    slog.LevelError,
		Assets: application.AssetOptions{
			Handler: createAssetHandler(),
		},
		Mac: application.MacOptions{
			ApplicationShouldTerminateAfterLastWindowClosed: true,
		},
	})

	// Create a new window with enhanced configuration for chat app
	// Features translucent background with native macOS blur effects
	app.Window.NewWithOptions(application.WebviewWindowOptions{
		Title:  "Kawai Chat",
		Width:  1000,
		Height: 700,
		Mac: application.MacWindow{
			InvisibleTitleBarHeight: 28,
			Backdrop:                application.MacBackdropTranslucent,
			TitleBar:                application.MacTitleBarHiddenInset,
			DisableShadow:           false,
		},
		BackgroundColour: application.NewRGBA(0, 0, 0, 0), // Fully transparent background
		URL:              "/",
		MinWidth:         800,
		MinHeight:        600,
	})

	// Create a goroutine that emits an event containing the current time every second.
	// The frontend can listen to this event and update the UI accordingly.
	go func() {
		for {
			now := time.Now().Format(time.RFC1123)
			app.Event.Emit("time", now)
			time.Sleep(time.Second)
		}
	}()

	// Run the application. This blocks until the application has been exited.
	if err := app.Run(); err != nil {
		log.Fatal(err)
	}
}
