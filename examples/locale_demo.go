/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package main

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/jeandeaual/go-locale"
	vscode_go "github.com/yudaprama/kawai-agent/vscode_go"
)

func main() {
	fmt.Println("🌐 Go-Locale Integration Demo")
	fmt.Println("=============================")

	// Direct go-locale usage
	fmt.Println("\n📍 Direct go-locale usage:")
	userLocales, err := locale.GetLocales()
	if err != nil {
		log.Printf("Error getting locales: %v", err)
	} else {
		fmt.Printf("System locales: %v\n", userLocales)
		if len(userLocales) > 0 {
			fmt.Printf("Primary locale: %s\n", userLocales[0])
		}
	}

	// VSCode service integration
	fmt.Println("\n🚀 VSCode Service Integration:")
	service, err := vscode_go.NewVSCodeService()
	if err != nil {
		log.Fatalf("Failed to create VSCode service: %v", err)
	}
	defer service.Shutdown()

	// Get comprehensive locale information
	localeInfo := service.GetLocaleInfo()
	
	fmt.Printf("System locales: %v\n", localeInfo["systemLocales"])
	fmt.Printf("System languages: %v\n", localeInfo["systemLanguages"])
	fmt.Printf("Preferred language: %s\n", localeInfo["preferredLanguage"])
	fmt.Printf("Processed Zh locale: %s\n", localeInfo["processedZhLocale"])
	fmt.Printf("User defined locale: %s\n", localeInfo["userDefinedLocale"])

	// Get NLS configuration
	nlsConfig := service.GetNLSConfiguration()
	if nlsConfig != nil {
		fmt.Println("\n📋 NLS Configuration:")
		nlsJSON, _ := json.MarshalIndent(nlsConfig, "", "  ")
		fmt.Println(string(nlsJSON))
	}

	// Get VSCode info
	fmt.Println("\n📦 VSCode Service Info:")
	vscodeInfo := service.GetVSCodeInfo()
	vscodeJSON, _ := json.MarshalIndent(vscodeInfo, "", "  ")
	fmt.Println(string(vscodeJSON))

	fmt.Println("\n✅ Demo completed successfully!")
}