/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../base/common/lifecycle.js';
import { ICommandDetectionCapability } from '../../../../../platform/terminal/common/capabilities/capabilities.js';
import { IPromptInputModel } from '../../../../../platform/terminal/common/capabilities/commandDetection/promptInputModel.js';
import { ITerminalCompletion, TerminalCompletionItemKind } from './terminalCompletionItem.js';

export class TerminalSuggestTelemetry extends Disposable {
	private _acceptedCompletions: Array<{ label: string; kind?: string; sessionId: string; provider: string }> | undefined;

	private _kindMap = new Map<number, string>([
		[TerminalCompletionItemKind.File, 'File'],
		[TerminalCompletionItemKind.Folder, 'Folder'],
		[TerminalCompletionItemKind.Method, 'Method'],
		[TerminalCompletionItemKind.Alias, 'Alias'],
		[TerminalCompletionItemKind.Argument, 'Argument'],
		[TerminalCompletionItemKind.Option, 'Option'],
		[TerminalCompletionItemKind.OptionValue, 'Option Value'],
		[TerminalCompletionItemKind.Flag, 'Flag'],
		[TerminalCompletionItemKind.InlineSuggestion, 'Inline Suggestion'],
		[TerminalCompletionItemKind.InlineSuggestionAlwaysOnTop, 'Inline Suggestion'],
	]);

	constructor(
		commandDetection: ICommandDetectionCapability,
		private readonly _promptInputModel: IPromptInputModel,
	) {
		super();
		this._register(commandDetection.onCommandFinished((e) => {
			this._acceptedCompletions = undefined;
		}));
		this._register(this._promptInputModel.onDidInterrupt(() => {
			this._acceptedCompletions = undefined;
		}));
	}
	acceptCompletion(sessionId: string, completion: ITerminalCompletion | undefined, commandLine?: string): void {
		if (!completion || !commandLine) {
			this._acceptedCompletions = undefined;
			return;
		}
		this._acceptedCompletions = this._acceptedCompletions || [];
		this._acceptedCompletions.push({ label: typeof completion.label === 'string' ? completion.label : completion.label.label, kind: this._kindMap.get(completion.kind!), sessionId, provider: completion.provider });
	}
}