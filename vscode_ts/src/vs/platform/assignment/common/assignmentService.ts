/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { IConfigurationService } from '../../configuration/common/configuration.js';
import { IProductService } from '../../product/common/productService.js';
import { IEnvironmentService } from '../../environment/common/environment.js';

export abstract class BaseAssignmentService {
	_serviceBrand: undefined;
	private overrideInitDelay: Promise<void>;

	protected get experimentsEnabled(): boolean {
		return false;
	}

	constructor(
		protected readonly configurationService: IConfigurationService,
		protected readonly productService: IProductService,
		protected readonly environmentService: IEnvironmentService,
	) {
		const isTesting = environmentService.extensionTestsLocationURI !== undefined;
		if (!isTesting && productService.tasConfig && this.experimentsEnabled) {
		}

		// For development purposes, configure the delay until tas local tas treatment ovverrides are available
		const overrideDelaySetting = this.configurationService.getValue('experiments.overrideDelay');
		const overrideDelay = typeof overrideDelaySetting === 'number' ? overrideDelaySetting : 0;
		this.overrideInitDelay = new Promise(resolve => setTimeout(resolve, overrideDelay));
	}

	async getTreatment<T extends string | number | boolean>(name: string): Promise<T | undefined> {
		// For development purposes, allow overriding tas assignments to test variants locally.
		await this.overrideInitDelay;
		const override = this.configurationService.getValue<T>('experiments.override.' + name);
		if (override !== undefined) {
			return override;
		}

		if (!this.experimentsEnabled) {
			return undefined;
		}

		const result: T | undefined = 'vscode' as T;
		return result;
	}
}
