**/build/*/**/*.js
**/dist/**/*.js
**/extensions/**/*.d.ts
**/extensions/**/build/**
**/extensions/**/colorize-fixtures/**
**/extensions/css-language-features/server/test/pathCompletionFixtures/**
**/extensions/html-language-features/server/lib/jquery.d.ts
**/extensions/html-language-features/server/src/test/pathCompletionFixtures/**
**/extensions/ipynb/notebook-out/**
**/extensions/markdown-language-features/media/**
**/extensions/markdown-language-features/notebook-out/**
**/extensions/markdown-math/notebook-out/**
**/extensions/notebook-renderers/renderer-out/index.js
**/extensions/simple-browser/media/index.js
**/extensions/terminal-suggest/src/completions/upstream/**
**/extensions/terminal-suggest/src/shell/zshBuiltinsCache.ts
**/extensions/terminal-suggest/src/shell/fishBuiltinsCache.ts
**/extensions/terminal-suggest/third_party/**
**/extensions/typescript-language-features/test-workspace/**
**/extensions/typescript-language-features/extension.webpack.config.js
**/extensions/typescript-language-features/extension-browser.webpack.config.js
**/extensions/typescript-language-features/package-manager/node-maintainer/**
**/extensions/vscode-api-tests/testWorkspace/**
**/extensions/vscode-api-tests/testWorkspace2/**
**/fixtures/**
**/node_modules/**
**/out-*/**/*.js
**/out-editor-*/**
**/out/**/*.js
**/src/**/dompurify.js
**/src/**/marked.js
**/src/**/semver.js
**/src/typings/**/*.d.ts
**/src/vs/*/**/*.d.ts
**/src/vs/base/test/common/filters.perf.data.js
**/src/vs/loader.js
**/test/unit/assert.js
**/test/automation/out/**
**/typings/**
**/.build/**
!.vscode
