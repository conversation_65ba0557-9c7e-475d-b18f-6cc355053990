package internal

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
)

// VSCodeService provides direct VS Code backend functionality for Wails v3
// This service is converted directly from vscode_go/main.go for clean integration
type VSCodeService struct {
	// VS Code configuration (from vscode_go/main.go)
	dirname                   string
	portable                  PortableConfiguration
	userDataPath              string
	codeCachePath             string
	nlsConfigurationPromise   *INLSConfiguration
	nlsConfigurationPromiseMu sync.Mutex
	product                   *IProductConfiguration

	// Service state
	initialized bool
	ctx         context.Context
	cancel      context.CancelFunc
	mutex       sync.RWMutex
}

// INLSConfiguration represents the NLS configuration (from vscode_go/main.go)
type INLSConfiguration struct {
	UserLocale          string            `json:"userLocale"`
	OSLocale            string            `json:"osLocale"`
	ResolvedLanguage    string            `json:"resolvedLanguage"`
	DefaultMessagesFile string            `json:"defaultMessagesFile"`
	Locale              string            `json:"locale"`
	AvailableLanguages  map[string]string `json:"availableLanguages"`
}

// IProductConfiguration represents the product configuration (from vscode_go/main.go)
type IProductConfiguration struct {
	NameShort string  `json:"nameShort"`
	Commit    *string `json:"commit,omitempty"`
}

// PortableConfiguration represents portable mode configuration
type PortableConfiguration struct {
	IsPortable       bool
	PortableDataPath string
}

// NewVSCodeService creates a new VS Code service instance
func NewVSCodeService() (*VSCodeService, error) {
	ctx, cancel := context.WithCancel(context.Background())

	service := &VSCodeService{
		ctx:    ctx,
		cancel: cancel,
	}

	// Initialize like vscode_go/main.go init()
	if err := service.initializeDirname(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize dirname: %w", err)
	}

	if err := service.initialize(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize VS Code service: %w", err)
	}

	return service, nil
}

// initializeDirname sets up the directory name equivalent to __dirname in Node.js
func (vs *VSCodeService) initializeDirname() error {
	if execPath, err := os.Executable(); err == nil {
		vs.dirname = filepath.Dir(execPath)
		return nil
	} else {
		return fmt.Errorf("failed to get executable path: %w", err)
	}
}

// initialize sets up VS Code service (equivalent to vscode_go/main.go main())
func (vs *VSCodeService) initialize() error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	log.Println("🚀 Initializing VS Code service...")

	// Initialize product configuration
	vs.product = &IProductConfiguration{
		NameShort: "Kawai Agent VS Code",
		Commit:    stringPtr("dev"),
	}

	// Enable portable support
	vs.portable = vs.configurePortable(vs.product)

	// Set userData path before app 'ready' event
	productNameShort := "kawai-agent"
	if vs.product != nil && vs.product.NameShort != "" {
		productNameShort = vs.product.NameShort
	}

	var err error
	vs.userDataPath, err = vs.getUserDataPath(productNameShort)
	if err != nil {
		return fmt.Errorf("error getting user data path: %w", err)
	}

	// Handle UNC paths on Windows
	if runtime.GOOS == "windows" {
		userDataUNCHost := vs.getUNCHost(vs.userDataPath)
		if userDataUNCHost != "" {
			vs.addUNCHostToAllowlist(userDataUNCHost)
		}
	}

	// Resolve code cache path
	vs.codeCachePath = vs.getCodeCachePath()

	// Set logs path before app 'ready' event if running portable
	if vs.portable.IsPortable {
		logsPath := filepath.Join(vs.userDataPath, "logs")
		os.Setenv("VSCODE_LOGS_PATH", logsPath)
	}

	// Initialize NLS configuration
	osLocale := vs.processZhLocale(vs.getPreferredSystemLanguage())
	userLocale := vs.getUserDefinedLocale()

	if userLocale != "" {
		vs.nlsConfigurationPromise = vs.resolveNLSConfigurationEarly(userLocale, osLocale)
	}

	// Pass in the locale to the application
	if runtime.GOOS == "windows" || runtime.GOOS == "linux" {
		electronLocale := "en"
		if userLocale != "" && userLocale != "qps-ploc" {
			electronLocale = userLocale
		}
		os.Setenv("VSCODE_LOCALE", electronLocale)
	}

	// Load our code once ready (equivalent to onReady())
	if err := vs.onReady(); err != nil {
		return fmt.Errorf("error during ready phase: %w", err)
	}

	vs.initialized = true
	log.Println("✅ VS Code service initialized successfully")

	return nil
}

// onReady handles the application ready event (from vscode_go/main.go)
func (vs *VSCodeService) onReady() error {
	// Create code cache directory if needed
	vs.mkdirpIgnoreError(vs.codeCachePath)

	// Resolve NLS configuration
	nlsConfig, err := vs.resolveNlsConfiguration()
	if err != nil {
		log.Printf("Error resolving NLS configuration: %v", err)
		// Continue with default configuration
		nlsConfig = &INLSConfiguration{
			UserLocale:          "en",
			OSLocale:            vs.getPreferredSystemLanguage(),
			ResolvedLanguage:    "en",
			DefaultMessagesFile: filepath.Join(vs.dirname, "nls.messages.json"),
			Locale:              "en",
			AvailableLanguages:  map[string]string{},
		}
	}

	// Start the application (equivalent to startup())
	return vs.startup(vs.codeCachePath, nlsConfig)
}

// startup is the main startup routine (from vscode_go/main.go)
func (vs *VSCodeService) startup(codeCachePath string, nlsConfig *INLSConfiguration) error {
	// Set environment variables
	nlsConfigJSON, _ := json.Marshal(nlsConfig)
	os.Setenv("VSCODE_NLS_CONFIG", string(nlsConfigJSON))

	if codeCachePath != "" {
		os.Setenv("VSCODE_CODE_CACHE_PATH", codeCachePath)
	} else {
		os.Setenv("VSCODE_CODE_CACHE_PATH", "")
	}

	// Bootstrap ESM
	if err := vs.bootstrapESM(); err != nil {
		return fmt.Errorf("error bootstrapping ESM: %w", err)
	}

	log.Println("✅ VS Code startup complete")
	log.Printf("📁 User data path: %s", vs.userDataPath)
	log.Printf("💾 Code cache path: %s", vs.codeCachePath)
	log.Printf("📦 Portable mode: %t", vs.portable.IsPortable)
	log.Printf("🌐 NLS language: %s", nlsConfig.ResolvedLanguage)

	return nil
}

// Wails v3 service interface methods

// Name returns the service name
func (vs *VSCodeService) Name() string {
	return "VSCodeService"
}

// Shutdown gracefully shuts down the VS Code service
func (vs *VSCodeService) Shutdown() error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	if !vs.initialized {
		return nil
	}

	log.Println("🔄 Shutting down VS Code service...")

	vs.cancel()
	vs.initialized = false

	log.Println("✅ VS Code service shut down successfully")
	return nil
}

// IsInitialized returns whether the service is initialized
func (vs *VSCodeService) IsInitialized() bool {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.initialized
}

// Context returns the service context
func (vs *VSCodeService) Context() context.Context {
	return vs.ctx
}

// Helper functions (from vscode_go/main.go)

// stringPtr returns a pointer to a string
func stringPtr(s string) *string {
	return &s
}

// configurePortable configures portable mode
func (vs *VSCodeService) configurePortable(product *IProductConfiguration) PortableConfiguration {
	// Simplified portable configuration - in real implementation this would check for portable.json
	return PortableConfiguration{
		IsPortable:       false,
		PortableDataPath: "",
	}
}

// getUserDataPath determines the user data path for VS Code
func (vs *VSCodeService) getUserDataPath(productName string) (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get home directory: %w", err)
	}

	var userDataPath string
	switch runtime.GOOS {
	case "windows":
		appData := os.Getenv("APPDATA")
		if appData == "" {
			appData = filepath.Join(homeDir, "AppData", "Roaming")
		}
		userDataPath = filepath.Join(appData, productName)
	case "darwin":
		userDataPath = filepath.Join(homeDir, "Library", "Application Support", productName)
	default: // linux and others
		configHome := os.Getenv("XDG_CONFIG_HOME")
		if configHome == "" {
			configHome = filepath.Join(homeDir, ".config")
		}
		userDataPath = filepath.Join(configHome, productName)
	}

	// Create directory if it doesn't exist
	if err := os.MkdirAll(userDataPath, 0755); err != nil {
		return "", fmt.Errorf("failed to create user data directory: %w", err)
	}

	return userDataPath, nil
}

// getUNCHost extracts UNC host from path (Windows only)
func (vs *VSCodeService) getUNCHost(path string) string {
	if runtime.GOOS != "windows" {
		return ""
	}

	if strings.HasPrefix(path, "\\\\") {
		parts := strings.Split(path[2:], "\\")
		if len(parts) > 0 {
			return parts[0]
		}
	}

	return ""
}

// addUNCHostToAllowlist adds UNC host to allowlist (Windows only)
func (vs *VSCodeService) addUNCHostToAllowlist(host string) {
	if runtime.GOOS != "windows" {
		return
	}

	// In a real implementation, this would add the host to Windows UNC allowlist
	log.Printf("🔧 Adding UNC host to allowlist: %s", host)
}

// getCodeCachePath determines the code cache path
func (vs *VSCodeService) getCodeCachePath() string {
	// Check command line arguments for --no-cached-data
	for _, arg := range os.Args {
		if arg == "--no-cached-data" {
			return ""
		}
	}

	// Running out of sources
	if os.Getenv("VSCODE_DEV") != "" {
		return ""
	}

	// Require commit id
	if vs.product == nil || vs.product.Commit == nil || *vs.product.Commit == "" {
		return ""
	}

	// Create code cache path
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return ""
	}

	var cacheDir string
	switch runtime.GOOS {
	case "windows":
		localAppData := os.Getenv("LOCALAPPDATA")
		if localAppData == "" {
			localAppData = filepath.Join(homeDir, "AppData", "Local")
		}
		cacheDir = filepath.Join(localAppData, "kawai-agent", "CachedData")
	case "darwin":
		cacheDir = filepath.Join(homeDir, "Library", "Caches", "kawai-agent", "CachedData")
	default: // linux and others
		cacheHome := os.Getenv("XDG_CACHE_HOME")
		if cacheHome == "" {
			cacheHome = filepath.Join(homeDir, ".cache")
		}
		cacheDir = filepath.Join(cacheHome, "kawai-agent", "CachedData")
	}

	codeCachePath := filepath.Join(cacheDir, *vs.product.Commit)

	// Create directory if it doesn't exist
	os.MkdirAll(codeCachePath, 0755)

	return codeCachePath
}

// processZhLocale processes Chinese locale (from vscode_go/main.go)
func (vs *VSCodeService) processZhLocale(locale string) string {
	// Simplified implementation - in real scenario this would handle Chinese locale variants
	return locale
}

// getPreferredSystemLanguage gets the preferred system language
func (vs *VSCodeService) getPreferredSystemLanguage() string {
	// Simplified implementation - in a real scenario, this would query the OS
	lang := os.Getenv("LANG")
	if lang == "" {
		return "en"
	}

	// Extract language code (e.g., "en_US.UTF-8" -> "en")
	if idx := strings.Index(lang, "_"); idx != -1 {
		lang = lang[:idx]
	}
	if idx := strings.Index(lang, "."); idx != -1 {
		lang = lang[:idx]
	}

	return lang
}

// getUserDefinedLocale gets user-defined locale
func (vs *VSCodeService) getUserDefinedLocale() string {
	// Check environment variable first
	if locale := os.Getenv("VSCODE_LOCALE"); locale != "" {
		return locale
	}

	// In a real implementation, this would check argv.json and other sources
	return ""
}

// resolveNLSConfigurationEarly resolves NLS configuration early
func (vs *VSCodeService) resolveNLSConfigurationEarly(userLocale, osLocale string) *INLSConfiguration {
	return &INLSConfiguration{
		UserLocale:          userLocale,
		OSLocale:            osLocale,
		ResolvedLanguage:    userLocale,
		DefaultMessagesFile: "",
		Locale:              userLocale,
		AvailableLanguages:  map[string]string{},
	}
}

// mkdirpIgnoreError creates directory recursively, ignoring errors
func (vs *VSCodeService) mkdirpIgnoreError(path string) {
	if path != "" {
		os.MkdirAll(path, 0755)
	}
}

// resolveNlsConfiguration resolves NLS configuration
func (vs *VSCodeService) resolveNlsConfiguration() (*INLSConfiguration, error) {
	vs.nlsConfigurationPromiseMu.Lock()
	defer vs.nlsConfigurationPromiseMu.Unlock()

	if vs.nlsConfigurationPromise != nil {
		return vs.nlsConfigurationPromise, nil
	}

	// Create default configuration
	osLocale := vs.getPreferredSystemLanguage()
	userLocale := vs.getUserDefinedLocale()
	if userLocale == "" {
		userLocale = "en"
	}

	return &INLSConfiguration{
		UserLocale:          userLocale,
		OSLocale:            osLocale,
		ResolvedLanguage:    userLocale,
		DefaultMessagesFile: filepath.Join(vs.dirname, "nls.messages.json"),
		Locale:              userLocale,
		AvailableLanguages:  map[string]string{},
	}, nil
}

// bootstrapESM bootstraps the ESM system
func (vs *VSCodeService) bootstrapESM() error {
	// In the original TypeScript version, this would bootstrap the ES module system
	// For Go, we'll just log that we've reached this point
	log.Println("🔧 Bootstrapping ESM system...")

	// In a real implementation, this would:
	// 1. Set up module resolution
	// 2. Initialize the workbench
	// 3. Load extensions
	// 4. Start the main VS Code process

	return nil
}

// Wails v3 Frontend Binding Methods
// These methods will be automatically exposed to the frontend via Wails bindings

// GetVSCodeInfo returns VS Code configuration information for the frontend
func (vs *VSCodeService) GetVSCodeInfo() map[string]interface{} {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()

	if !vs.initialized {
		return map[string]interface{}{
			"initialized": false,
			"error":       "VS Code service not initialized",
		}
	}

	info := map[string]interface{}{
		"initialized":   true,
		"userDataPath":  vs.userDataPath,
		"codeCachePath": vs.codeCachePath,
		"isPortable":    vs.portable.IsPortable,
		"dirname":       vs.dirname,
	}

	if vs.product != nil {
		info["product"] = map[string]interface{}{
			"nameShort": vs.product.NameShort,
			"commit":    vs.product.Commit,
		}
	}

	if vs.nlsConfigurationPromise != nil {
		info["nls"] = vs.nlsConfigurationPromise
	}

	return info
}

// GetUserDataPath returns the VS Code user data path
func (vs *VSCodeService) GetUserDataPath() string {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.userDataPath
}

// GetCodeCachePath returns the VS Code code cache path
func (vs *VSCodeService) GetCodeCachePath() string {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.codeCachePath
}

// IsPortable returns whether VS Code is running in portable mode
func (vs *VSCodeService) IsPortable() bool {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.portable.IsPortable
}

// GetNLSConfiguration returns the current NLS configuration
func (vs *VSCodeService) GetNLSConfiguration() *INLSConfiguration {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.nlsConfigurationPromise
}

// RestartVSCode restarts the VS Code service
func (vs *VSCodeService) RestartVSCode() error {
	log.Println("🔄 Restarting VS Code service...")

	// Shutdown current instance
	if err := vs.Shutdown(); err != nil {
		return fmt.Errorf("failed to shutdown VS Code service: %w", err)
	}

	// Reinitialize
	if err := vs.initialize(); err != nil {
		return fmt.Errorf("failed to reinitialize VS Code service: %w", err)
	}

	return nil
}
