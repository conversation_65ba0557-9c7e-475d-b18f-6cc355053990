package vscode

import (
	"context"
	"fmt"
	"log"
	"sync"

	"github.com/yudaprama/kawai-agent/internal/vscode/adapters"
	"github.com/yudaprama/kawai-agent/internal/vscode/core"
	"github.com/yudaprama/kawai-agent/internal/vscode/platform"
	"github.com/yudaprama/kawai-agent/internal/vscode/types"
	"github.com/yudaprama/kawai-agent/internal/vscode/workbench"
)

// VSCodeService provides VS Code backend functionality for Kawai Agent
// This service replaces VS Code's Node.js backend with Go implementation
// while maintaining compatibility with the existing VS Code frontend
type VSCodeService struct {
	// Platform service adapter (bridges comprehensive vscode_go services)
	platformAdapter *adapters.PlatformServiceAdapter

	// Core services
	app       *core.Application
	config    *core.ConfigurationService
	workspace *core.WorkspaceService
	ipc       *core.IPCService

	// Platform services (legacy - accessed through adapter)
	fileSystem  *platform.FileSystemService
	environment *platform.EnvironmentService
	storage     *platform.StorageService
	telemetry   *platform.TelemetryService

	// Workbench services
	editor   *workbench.EditorService
	themes   *workbench.ThemeService
	search   *workbench.SearchService
	terminal *workbench.TerminalService

	// Service state
	initialized bool
	mutex       sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewVSCodeService creates a new VS Code service instance
func NewVSCodeService() (*VSCodeService, error) {
	ctx, cancel := context.WithCancel(context.Background())

	service := &VSCodeService{
		ctx:    ctx,
		cancel: cancel,
	}

	if err := service.initialize(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize VS Code service: %w", err)
	}

	return service, nil
}

// initialize sets up all VS Code services in the correct order
func (vs *VSCodeService) initialize() error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	log.Println("🚀 Initializing VS Code backend services...")

	// Initialize platform services first
	if err := vs.initializePlatformServices(); err != nil {
		return fmt.Errorf("failed to initialize platform services: %w", err)
	}

	// Initialize core services
	if err := vs.initializeCoreServices(); err != nil {
		return fmt.Errorf("failed to initialize core services: %w", err)
	}

	// Initialize workbench services
	if err := vs.initializeWorkbenchServices(); err != nil {
		return fmt.Errorf("failed to initialize workbench services: %w", err)
	}

	vs.initialized = true
	log.Println("✅ VS Code backend services initialized successfully")

	return nil
}

// initializePlatformServices initializes platform-level services using the adapter
func (vs *VSCodeService) initializePlatformServices() error {
	var err error

	// Initialize platform service adapter (bridges comprehensive vscode_go services)
	vs.platformAdapter, err = adapters.NewPlatformServiceAdapter(vs.ctx)
	if err != nil {
		return fmt.Errorf("failed to create platform service adapter: %w", err)
	}

	// Get legacy services from adapter for backward compatibility
	vs.environment = vs.platformAdapter.GetLegacyEnvironmentService()
	vs.fileSystem = vs.platformAdapter.GetLegacyFileSystemService()
	vs.storage = vs.platformAdapter.GetLegacyStorageService()
	vs.telemetry = vs.platformAdapter.GetLegacyTelemetryService()

	log.Println("✅ Platform services initialized successfully (via adapter)")
	return nil
}

// initializeCoreServices initializes core VS Code services
func (vs *VSCodeService) initializeCoreServices() error {
	var err error

	// Configuration service - manages all configuration
	vs.config, err = core.NewConfigurationService(vs.fileSystem, vs.storage)
	if err != nil {
		return fmt.Errorf("failed to create configuration service: %w", err)
	}

	// Application service - manages application lifecycle
	vs.app, err = core.NewApplication(vs.config, vs.telemetry)
	if err != nil {
		return fmt.Errorf("failed to create application service: %w", err)
	}

	// Workspace service - manages workspace operations
	vs.workspace, err = core.NewWorkspaceService(vs.fileSystem, vs.config)
	if err != nil {
		return fmt.Errorf("failed to create workspace service: %w", err)
	}

	// IPC service - handles inter-process communication
	vs.ipc, err = core.NewIPCService(vs.app)
	if err != nil {
		return fmt.Errorf("failed to create IPC service: %w", err)
	}

	log.Println("✅ Core services initialized")
	return nil
}

// initializeWorkbenchServices initializes workbench-level services
func (vs *VSCodeService) initializeWorkbenchServices() error {
	var err error

	// Editor service - manages editor functionality
	vs.editor, err = workbench.NewEditorService(vs.fileSystem, vs.config)
	if err != nil {
		return fmt.Errorf("failed to create editor service: %w", err)
	}

	// Theme service - manages themes and styling
	vs.themes, err = workbench.NewThemeService(vs.config, vs.storage)
	if err != nil {
		return fmt.Errorf("failed to create theme service: %w", err)
	}

	// Search service - handles file and content search
	vs.search, err = workbench.NewSearchService(vs.fileSystem, vs.workspace)
	if err != nil {
		return fmt.Errorf("failed to create search service: %w", err)
	}

	// Terminal service - manages terminal functionality
	vs.terminal, err = workbench.NewTerminalService(vs.config)
	if err != nil {
		return fmt.Errorf("failed to create terminal service: %w", err)
	}

	log.Println("✅ Workbench services initialized")
	return nil
}

// Name returns the service name for Wails registration
func (vs *VSCodeService) Name() string {
	return "VSCodeService"
}

// Shutdown gracefully shuts down the VS Code service
func (vs *VSCodeService) Shutdown() error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	if !vs.initialized {
		return nil
	}

	log.Println("🔄 Shutting down VS Code backend services...")

	// Cancel context to stop all operations
	vs.cancel()

	// Shutdown services in reverse order
	if vs.terminal != nil {
		vs.terminal.Shutdown()
	}
	if vs.search != nil {
		vs.search.Shutdown()
	}
	if vs.themes != nil {
		vs.themes.Shutdown()
	}
	if vs.editor != nil {
		vs.editor.Shutdown()
	}
	if vs.ipc != nil {
		vs.ipc.Shutdown()
	}
	if vs.workspace != nil {
		vs.workspace.Shutdown()
	}
	if vs.app != nil {
		vs.app.Shutdown()
	}
	if vs.config != nil {
		vs.config.Shutdown()
	}
	// Shutdown platform adapter (handles all platform services)
	if vs.platformAdapter != nil {
		vs.platformAdapter.Shutdown()
	}

	vs.initialized = false
	log.Println("✅ VS Code backend services shut down successfully")

	return nil
}

// IsInitialized returns whether the service is initialized
func (vs *VSCodeService) IsInitialized() bool {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.initialized
}

// GetStatus returns the current status of the VS Code service
func (vs *VSCodeService) GetStatus() types.ServiceStatus {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()

	if !vs.initialized {
		return types.ServiceStatus{
			Name:        "VSCodeService",
			Status:      "stopped",
			Initialized: false,
			Message:     "Service not initialized",
		}
	}

	return types.ServiceStatus{
		Name:        "VSCodeService",
		Status:      "running",
		Initialized: true,
		Message:     "All services running normally",
		Services: map[string]string{
			"environment": "running",
			"fileSystem":  "running",
			"storage":     "running",
			"telemetry":   "running",
			"config":      "running",
			"app":         "running",
			"workspace":   "running",
			"ipc":         "running",
			"editor":      "running",
			"themes":      "running",
			"search":      "running",
			"terminal":    "running",
		},
	}
}

// Context returns the service context
func (vs *VSCodeService) Context() context.Context {
	return vs.ctx
}

// Frontend API Methods - These methods are exposed to the Wails frontend

// OpenWorkspace opens a workspace from a path
func (vs *VSCodeService) OpenWorkspace(path string) error {
	if !vs.IsInitialized() {
		return fmt.Errorf("VS Code service not initialized")
	}
	return vs.workspace.OpenWorkspace(path)
}

// CloseWorkspace closes the current workspace
func (vs *VSCodeService) CloseWorkspace() error {
	if !vs.IsInitialized() {
		return fmt.Errorf("VS Code service not initialized")
	}
	return vs.workspace.CloseWorkspace()
}

// GetCurrentWorkspace returns the current workspace information
func (vs *VSCodeService) GetCurrentWorkspace() *types.WorkspaceInfo {
	if !vs.IsInitialized() {
		return nil
	}
	return vs.workspace.GetCurrentWorkspace()
}

// GetWorkspaceFiles returns all files in the current workspace
func (vs *VSCodeService) GetWorkspaceFiles() ([]types.FileInfo, error) {
	if !vs.IsInitialized() {
		return nil, fmt.Errorf("VS Code service not initialized")
	}
	return vs.workspace.GetWorkspaceFiles()
}

// GetFileTree returns the file tree for the current workspace
func (vs *VSCodeService) GetFileTree() (*types.FileTreeNode, error) {
	if !vs.IsInitialized() {
		return nil, fmt.Errorf("VS Code service not initialized")
	}
	return vs.workspace.GetFileTree()
}

// ReadFile reads the contents of a file
func (vs *VSCodeService) ReadFile(path string) (string, error) {
	if !vs.IsInitialized() {
		return "", fmt.Errorf("VS Code service not initialized")
	}
	return vs.fileSystem.ReadFile(path)
}

// WriteFile writes content to a file
func (vs *VSCodeService) WriteFile(path, content string) error {
	if !vs.IsInitialized() {
		return fmt.Errorf("VS Code service not initialized")
	}
	return vs.fileSystem.WriteFile(path, content)
}

// DeleteFile deletes a file
func (vs *VSCodeService) DeleteFile(path string) error {
	if !vs.IsInitialized() {
		return fmt.Errorf("VS Code service not initialized")
	}
	return vs.fileSystem.DeleteFile(path)
}

// CreateDirectory creates a directory
func (vs *VSCodeService) CreateDirectory(path string) error {
	if !vs.IsInitialized() {
		return fmt.Errorf("VS Code service not initialized")
	}
	return vs.fileSystem.CreateDirectory(path)
}

// GetFileInfo returns information about a file or directory
func (vs *VSCodeService) GetFileInfo(path string) (*types.FileInfo, error) {
	if !vs.IsInitialized() {
		return nil, fmt.Errorf("VS Code service not initialized")
	}
	return vs.fileSystem.GetFileInfo(path)
}

// SearchFiles searches for files and content in the workspace
func (vs *VSCodeService) SearchFiles(options types.SearchOptions) ([]types.SearchResult, error) {
	if !vs.IsInitialized() {
		return nil, fmt.Errorf("VS Code service not initialized")
	}
	return vs.search.Search(options)
}

// GetConfiguration returns configuration values for a section
func (vs *VSCodeService) GetConfiguration(section string) map[string]interface{} {
	if !vs.IsInitialized() {
		return make(map[string]interface{})
	}
	return vs.config.GetConfiguration(section)
}

// SetConfiguration sets a configuration value
func (vs *VSCodeService) SetConfiguration(key string, value interface{}, scope string) error {
	if !vs.IsInitialized() {
		return fmt.Errorf("VS Code service not initialized")
	}
	return vs.config.SetValue(key, value, scope)
}

// GetApplicationInfo returns information about the VS Code application
func (vs *VSCodeService) GetApplicationInfo() map[string]interface{} {
	if !vs.IsInitialized() {
		return map[string]interface{}{
			"initialized": false,
			"error":       "VS Code service not initialized",
		}
	}
	return vs.app.GetApplicationInfo()
}

// GetPerformanceMetrics returns performance metrics
func (vs *VSCodeService) GetPerformanceMetrics() map[string]interface{} {
	if !vs.IsInitialized() {
		return map[string]interface{}{
			"initialized": false,
		}
	}
	return vs.app.GetPerformanceMetrics()
}
