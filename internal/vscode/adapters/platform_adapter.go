package adapters

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	// Import comprehensive VS Code services from vscode_go
	configurationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	dialogscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/dialogs/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	storagecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/storage/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"

	// Import existing internal services for compatibility
	"github.com/yudaprama/kawai-agent/internal/vscode/platform"
	"github.com/yudaprama/kawai-agent/internal/vscode/types"
)

// VSCodeInitializationConfig represents VS Code initialization configuration
type VSCodeInitializationConfig struct {
	UserDataPath  string
	CodeCachePath string
	IsPortable    bool
	NLSConfig     *NLSConfiguration
	ProductConfig *ProductConfiguration
}

// NLSConfiguration represents the NLS (National Language Support) configuration
type NLSConfiguration struct {
	UserLocale          string            `json:"userLocale"`
	OSLocale            string            `json:"osLocale"`
	ResolvedLanguage    string            `json:"resolvedLanguage"`
	DefaultMessagesFile string            `json:"defaultMessagesFile"`
	Locale              string            `json:"locale"`
	AvailableLanguages  map[string]string `json:"availableLanguages"`
}

// ProductConfiguration represents the product configuration
type ProductConfiguration struct {
	NameShort string  `json:"nameShort"`
	Commit    *string `json:"commit,omitempty"`
}

// PlatformServiceAdapter bridges comprehensive vscode_go services with internal/vscode architecture
// This allows us to use the full-featured vscode_go services while maintaining Wails v3 compatibility
type PlatformServiceAdapter struct {
	// VS Code initialization configuration
	initConfig *VSCodeInitializationConfig

	// Comprehensive VS Code services from vscode_go
	environmentService   environmentcommon.IEnvironmentService
	storageService       storagecommon.IStorageService
	fileService          filescommon.IFileService
	workspaceService     workspacecommon.IWorkspaceContextService
	dialogService        dialogscommon.IDialogService
	configurationService configurationcommon.IConfigurationService

	// Legacy services for backward compatibility
	legacyEnvironment *platform.EnvironmentService
	legacyStorage     *platform.StorageService
	legacyFileSystem  *platform.FileSystemService
	legacyTelemetry   *platform.TelemetryService

	// Service state
	initialized bool
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewPlatformServiceAdapter creates a new platform service adapter
func NewPlatformServiceAdapter(ctx context.Context) (*PlatformServiceAdapter, error) {
	adapterCtx, cancel := context.WithCancel(ctx)

	adapter := &PlatformServiceAdapter{
		ctx:    adapterCtx,
		cancel: cancel,
	}

	if err := adapter.initialize(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize platform service adapter: %w", err)
	}

	return adapter, nil
}

// initialize sets up all platform services with VS Code initialization
func (psa *PlatformServiceAdapter) initialize() error {
	log.Println("🔧 Initializing Platform Service Adapter...")

	// Step 1: Initialize VS Code configuration (like vscode_go/main.go)
	if err := psa.initializeVSCodeConfiguration(); err != nil {
		return fmt.Errorf("failed to initialize VS Code configuration: %w", err)
	}

	// Step 2: Initialize legacy services for backward compatibility
	if err := psa.initializeLegacyServices(); err != nil {
		return fmt.Errorf("failed to initialize legacy services: %w", err)
	}

	// Step 3: Set up VS Code environment variables
	if err := psa.setupVSCodeEnvironment(); err != nil {
		return fmt.Errorf("failed to setup VS Code environment: %w", err)
	}

	// TODO: Initialize comprehensive services in next phase
	// if err := psa.initializeComprehensiveServices(); err != nil {
	//     return fmt.Errorf("failed to initialize comprehensive services: %w", err)
	// }

	psa.initialized = true
	log.Println("✅ Platform Service Adapter initialized successfully")

	return nil
}

// initializeLegacyServices initializes the existing internal/vscode services
func (psa *PlatformServiceAdapter) initializeLegacyServices() error {
	var err error

	// Environment service
	psa.legacyEnvironment, err = platform.NewEnvironmentService()
	if err != nil {
		return fmt.Errorf("failed to create legacy environment service: %w", err)
	}

	// File system service
	psa.legacyFileSystem, err = platform.NewFileSystemService(psa.legacyEnvironment)
	if err != nil {
		return fmt.Errorf("failed to create legacy file system service: %w", err)
	}

	// Storage service
	psa.legacyStorage, err = platform.NewStorageService(psa.legacyEnvironment, psa.legacyFileSystem)
	if err != nil {
		return fmt.Errorf("failed to create legacy storage service: %w", err)
	}

	// Telemetry service
	psa.legacyTelemetry, err = platform.NewTelemetryService(psa.legacyEnvironment)
	if err != nil {
		return fmt.Errorf("failed to create legacy telemetry service: %w", err)
	}

	return nil
}

// initializeVSCodeConfiguration initializes VS Code configuration (from vscode_go/main.go)
func (psa *PlatformServiceAdapter) initializeVSCodeConfiguration() error {
	log.Println("🔧 Initializing VS Code configuration...")

	// Initialize product configuration
	productConfig := &ProductConfiguration{
		NameShort: "Kawai Agent VS Code",
		Commit:    stringPtr("dev"),
	}

	// Get user data path
	userDataPath, err := psa.getUserDataPath("kawai-agent")
	if err != nil {
		return fmt.Errorf("failed to get user data path: %w", err)
	}

	// Get code cache path
	codeCachePath := psa.getCodeCachePath(productConfig)

	// Initialize NLS configuration
	nlsConfig := psa.initializeNLSConfiguration()

	// Store initialization configuration
	psa.initConfig = &VSCodeInitializationConfig{
		UserDataPath:  userDataPath,
		CodeCachePath: codeCachePath,
		IsPortable:    false, // TODO: Implement portable mode detection
		NLSConfig:     nlsConfig,
		ProductConfig: productConfig,
	}

	log.Printf("✅ VS Code configuration initialized - UserData: %s, CodeCache: %s",
		userDataPath, codeCachePath)

	return nil
}

// setupVSCodeEnvironment sets up VS Code environment variables
func (psa *PlatformServiceAdapter) setupVSCodeEnvironment() error {
	if psa.initConfig == nil {
		return fmt.Errorf("VS Code configuration not initialized")
	}

	log.Println("🔧 Setting up VS Code environment variables...")

	// Set NLS configuration
	if psa.initConfig.NLSConfig != nil {
		nlsConfigJSON, _ := json.Marshal(psa.initConfig.NLSConfig)
		os.Setenv("VSCODE_NLS_CONFIG", string(nlsConfigJSON))
	}

	// Set code cache path
	if psa.initConfig.CodeCachePath != "" {
		os.Setenv("VSCODE_CODE_CACHE_PATH", psa.initConfig.CodeCachePath)
	}

	// Set user data path
	os.Setenv("VSCODE_USER_DATA_PATH", psa.initConfig.UserDataPath)

	// Set locale for the application
	if runtime.GOOS == "windows" || runtime.GOOS == "linux" {
		locale := "en"
		if psa.initConfig.NLSConfig != nil && psa.initConfig.NLSConfig.UserLocale != "" {
			locale = psa.initConfig.NLSConfig.UserLocale
		}
		os.Setenv("VSCODE_LOCALE", locale)
	}

	log.Println("✅ VS Code environment variables set up successfully")
	return nil
}

// initializeComprehensiveServices initializes the comprehensive vscode_go services
// TODO: Implement this when we create the service implementations
func (psa *PlatformServiceAdapter) initializeComprehensiveServices() error {
	// This will be implemented in the next phase
	return nil
}

// GetLegacyEnvironmentService returns the legacy environment service for backward compatibility
func (psa *PlatformServiceAdapter) GetLegacyEnvironmentService() *platform.EnvironmentService {
	return psa.legacyEnvironment
}

// GetLegacyFileSystemService returns the legacy file system service for backward compatibility
func (psa *PlatformServiceAdapter) GetLegacyFileSystemService() *platform.FileSystemService {
	return psa.legacyFileSystem
}

// GetLegacyStorageService returns the legacy storage service for backward compatibility
func (psa *PlatformServiceAdapter) GetLegacyStorageService() *platform.StorageService {
	return psa.legacyStorage
}

// GetLegacyTelemetryService returns the legacy telemetry service for backward compatibility
func (psa *PlatformServiceAdapter) GetLegacyTelemetryService() *platform.TelemetryService {
	return psa.legacyTelemetry
}

// IsInitialized returns whether the adapter is initialized
func (psa *PlatformServiceAdapter) IsInitialized() bool {
	return psa.initialized
}

// Shutdown gracefully shuts down all services
func (psa *PlatformServiceAdapter) Shutdown() error {
	if !psa.initialized {
		return nil
	}

	log.Println("🔧 Shutting down Platform Service Adapter...")

	// Shutdown legacy services
	if psa.legacyTelemetry != nil {
		psa.legacyTelemetry.Shutdown()
	}
	if psa.legacyStorage != nil {
		psa.legacyStorage.Shutdown()
	}
	if psa.legacyFileSystem != nil {
		psa.legacyFileSystem.Shutdown()
	}
	if psa.legacyEnvironment != nil {
		psa.legacyEnvironment.Shutdown()
	}

	// TODO: Shutdown comprehensive services when implemented

	psa.cancel()
	psa.initialized = false

	log.Println("✅ Platform Service Adapter shut down successfully")
	return nil
}

// GetStatus returns the current status of the adapter
func (psa *PlatformServiceAdapter) GetStatus() types.ServiceStatus {
	if !psa.initialized {
		return types.ServiceStatus{
			Name:        "PlatformServiceAdapter",
			Status:      "stopped",
			Initialized: false,
			Message:     "Adapter not initialized",
		}
	}

	return types.ServiceStatus{
		Name:        "PlatformServiceAdapter",
		Status:      "running",
		Initialized: true,
		Message:     "All platform services running normally",
		Services: map[string]string{
			"legacyEnvironment": "running",
			"legacyFileSystem":  "running",
			"legacyStorage":     "running",
			"legacyTelemetry":   "running",
			// TODO: Add comprehensive service statuses
		},
	}
}

// Helper functions for VS Code initialization (from vscode_go/main.go)

// stringPtr returns a pointer to a string
func stringPtr(s string) *string {
	return &s
}

// getUserDataPath determines the user data path for VS Code
func (psa *PlatformServiceAdapter) getUserDataPath(productName string) (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get home directory: %w", err)
	}

	var userDataPath string
	switch runtime.GOOS {
	case "windows":
		appData := os.Getenv("APPDATA")
		if appData == "" {
			appData = filepath.Join(homeDir, "AppData", "Roaming")
		}
		userDataPath = filepath.Join(appData, productName)
	case "darwin":
		userDataPath = filepath.Join(homeDir, "Library", "Application Support", productName)
	default: // linux and others
		configHome := os.Getenv("XDG_CONFIG_HOME")
		if configHome == "" {
			configHome = filepath.Join(homeDir, ".config")
		}
		userDataPath = filepath.Join(configHome, productName)
	}

	// Create directory if it doesn't exist
	if err := os.MkdirAll(userDataPath, 0755); err != nil {
		return "", fmt.Errorf("failed to create user data directory: %w", err)
	}

	return userDataPath, nil
}

// getCodeCachePath determines the code cache path
func (psa *PlatformServiceAdapter) getCodeCachePath(productConfig *ProductConfiguration) string {
	// Check for --no-cached-data flag
	for _, arg := range os.Args {
		if arg == "--no-cached-data" {
			return ""
		}
	}

	// Running in development mode
	if os.Getenv("VSCODE_DEV") != "" {
		return ""
	}

	// Require commit id
	if productConfig == nil || productConfig.Commit == nil || *productConfig.Commit == "" {
		return ""
	}

	// Create code cache path
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return ""
	}

	var cacheDir string
	switch runtime.GOOS {
	case "windows":
		localAppData := os.Getenv("LOCALAPPDATA")
		if localAppData == "" {
			localAppData = filepath.Join(homeDir, "AppData", "Local")
		}
		cacheDir = filepath.Join(localAppData, "kawai-agent", "CachedData")
	case "darwin":
		cacheDir = filepath.Join(homeDir, "Library", "Caches", "kawai-agent", "CachedData")
	default: // linux and others
		cacheHome := os.Getenv("XDG_CACHE_HOME")
		if cacheHome == "" {
			cacheHome = filepath.Join(homeDir, ".cache")
		}
		cacheDir = filepath.Join(cacheHome, "kawai-agent", "CachedData")
	}

	codeCachePath := filepath.Join(cacheDir, *productConfig.Commit)

	// Create directory if it doesn't exist
	os.MkdirAll(codeCachePath, 0755)

	return codeCachePath
}

// initializeNLSConfiguration initializes the NLS configuration
func (psa *PlatformServiceAdapter) initializeNLSConfiguration() *NLSConfiguration {
	// Get OS locale
	osLocale := psa.getPreferredSystemLanguage()

	// Get user-defined locale (simplified)
	userLocale := os.Getenv("VSCODE_LOCALE")
	if userLocale == "" {
		userLocale = "en"
	}

	// Resolve language
	resolvedLanguage := userLocale
	if resolvedLanguage == "" {
		resolvedLanguage = "en"
	}

	return &NLSConfiguration{
		UserLocale:          userLocale,
		OSLocale:            osLocale,
		ResolvedLanguage:    resolvedLanguage,
		DefaultMessagesFile: "",
		Locale:              resolvedLanguage,
		AvailableLanguages:  map[string]string{},
	}
}

// getPreferredSystemLanguage gets the preferred system language
func (psa *PlatformServiceAdapter) getPreferredSystemLanguage() string {
	// Simplified implementation - in a real scenario, this would query the OS
	lang := os.Getenv("LANG")
	if lang == "" {
		return "en"
	}

	// Extract language code (e.g., "en_US.UTF-8" -> "en")
	if idx := strings.Index(lang, "_"); idx != -1 {
		lang = lang[:idx]
	}
	if idx := strings.Index(lang, "."); idx != -1 {
		lang = lang[:idx]
	}

	return lang
}
