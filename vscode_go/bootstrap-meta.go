/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package vscode_go

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"

	base "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

var (
	// Product configuration - global variable
	Product *base.IProductConfiguration
	// Package configuration - global variable
	Pkg map[string]interface{}
)

// loadProductConfiguration loads the product configuration
func loadProductConfiguration() (*base.IProductConfiguration, error) {
	// Check if this is a built version with embedded configuration
	// In the TypeScript version, this is done via build-time replacement
	// For Go, we'll check if the configuration is embedded or load from file

	productObj := &base.IProductConfiguration{
		// Default configuration that gets replaced during build
		// BUILD_INSERT_PRODUCT_CONFIGURATION marker would be replaced during build
	}

	// Check if we need to load from file (running out of sources)
	// This happens when BUILD_INSERT_PRODUCT_CONFIGURATION is not replaced
	if shouldLoadFromFile() {
		// Load from ../product.json
		productPath := filepath.Join("..", "product.json")
		productData, err := ioutil.ReadFile(productPath)
		if err != nil {
			return nil, err
		}

		if err := json.Unmarshal(productData, productObj); err != nil {
			return nil, err
		}
	}

	return productObj, nil
}

// loadPackageConfiguration loads the package configuration
func loadPackageConfiguration() (map[string]interface{}, error) {
	// Check if this is a built version with embedded configuration
	pkgObj := make(map[string]interface{})

	// Default configuration that gets replaced during build
	// BUILD_INSERT_PACKAGE_CONFIGURATION marker would be replaced during build
	pkgObj["BUILD_INSERT_PACKAGE_CONFIGURATION"] = "BUILD_INSERT_PACKAGE_CONFIGURATION"

	// Check if we need to load from file (running out of sources)
	if shouldLoadPackageFromFile(pkgObj) {
		// Load from ../package.json
		packagePath := filepath.Join("..", "package.json")
		packageData, err := ioutil.ReadFile(packagePath)
		if err != nil {
			return nil, err
		}

		if err := json.Unmarshal(packageData, &pkgObj); err != nil {
			return nil, err
		}
	}

	return pkgObj, nil
}

// shouldLoadFromFile determines if we should load product config from file
func shouldLoadFromFile() bool {
	// In a real build, the BUILD_INSERT_PRODUCT_CONFIGURATION would be replaced
	// For now, we'll always load from file since we're running from source
	return true
}

// shouldLoadPackageFromFile determines if we should load package config from file
func shouldLoadPackageFromFile(pkgObj map[string]interface{}) bool {
	// Check if the build marker is still present
	if marker, exists := pkgObj["BUILD_INSERT_PACKAGE_CONFIGURATION"]; exists {
		if markerStr, ok := marker.(string); ok && markerStr == "BUILD_INSERT_PACKAGE_CONFIGURATION" {
			return true
		}
	}
	return false
}

// init initializes the product and package configurations
func init() {
	var err error

	// Load product configuration
	Product, err = loadProductConfiguration()
	if err != nil {
		// If loading fails, create a minimal default configuration
		Product = &base.IProductConfiguration{
			Version:               "1.102.0-dev",
			NameShort:             "Code - OSS Dev",
			NameLong:              "Code - OSS Dev",
			ApplicationName:       "code-oss",
			DataFolderName:        ".vscode-oss",
			URLProtocol:           "code-oss",
			ServerApplicationName: "code-oss",
		}
	}

	// Load package configuration
	Pkg, err = loadPackageConfiguration()
	if err != nil {
		// If loading fails, create a minimal default configuration
		Pkg = map[string]interface{}{
			"name":    "vscode",
			"version": "1.102.0-dev",
		}
	}
}

// GetProduct returns the product configuration
func GetProduct() *base.IProductConfiguration {
	return Product
}

// GetPackage returns the package configuration
func GetPackage() map[string]interface{} {
	return Pkg
}
