/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package vscode_go

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	base "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// PortableConfiguration represents the portable mode configuration
type PortableConfiguration struct {
	PortableDataPath string `json:"portableDataPath"`
	IsPortable       bool   `json:"isPortable"`
}

func init() {
	// Workaround for handling SIGPIPE - in Go this is handled differently
	// Go's runtime handles SIGPIPE by default, so we don't need explicit handling

	setupCurrentWorkingDirectory()
}

// setupCurrentWorkingDirectory sets up the current working directory
func setupCurrentWorkingDirectory() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Fprintf(os.Stderr, "Error setting up working directory: %v\n", r)
		}
	}()

	// Store the current working directory in VSCODE_CWD environment variable
	// for consistent lookups, but make sure to only do this once unless
	// defined already from e.g. a parent process.
	if os.Getenv("VSCODE_CWD") == "" {
		if cwd, err := os.Getwd(); err == nil {
			os.Setenv("VSCODE_CWD", cwd)
		}
	}

	// Windows: always set application folder as current working dir
	if runtime.GOOS == "windows" {
		if execPath, err := os.Executable(); err == nil {
			if err := os.Chdir(filepath.Dir(execPath)); err != nil {
				fmt.Fprintf(os.Stderr, "Error changing directory: %v\n", err)
			}
		}
	}
}

// ConfigurePortable enables portable mode and returns the configuration
func ConfigurePortable(product *base.IProductConfiguration) PortableConfiguration {
	// Get the application root directory
	execPath, err := os.Executable()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error getting executable path: %v\n", err)
		return PortableConfiguration{IsPortable: false}
	}

	appRoot := filepath.Dir(execPath)

	getApplicationPath := func() string {
		if os.Getenv("VSCODE_DEV") != "" {
			return appRoot
		}

		switch runtime.GOOS {
		case "darwin":
			// On macOS, go up three levels from the executable
			return filepath.Dir(filepath.Dir(filepath.Dir(appRoot)))
		default:
			// On Windows and Linux, go up two levels
			return filepath.Dir(filepath.Dir(appRoot))
		}
	}

	getPortableDataPath := func() string {
		// Check if VSCODE_PORTABLE environment variable is set
		if portablePath := os.Getenv("VSCODE_PORTABLE"); portablePath != "" {
			return portablePath
		}

		applicationPath := getApplicationPath()

		switch runtime.GOOS {
		case "windows", "linux":
			return filepath.Join(applicationPath, "data")
		case "darwin":
			portableDataName := "vscode-portable-data" // default
			if product != nil && product.Portable != nil {
				portableDataName = *product.Portable
			} else if product != nil {
				portableDataName = product.ApplicationName + "-portable-data"
			}
			return filepath.Join(filepath.Dir(applicationPath), portableDataName)
		default:
			return filepath.Join(applicationPath, "data")
		}
	}

	portableDataPath := getPortableDataPath()

	// Check if portable mode is enabled
	// Portable mode is enabled if:
	// 1. The product doesn't have a "target" property (not a built version)
	// 2. The portable data path exists
	isPortable := true
	if product != nil && product.Target != nil {
		isPortable = false
	}

	if isPortable {
		if _, err := os.Stat(portableDataPath); os.IsNotExist(err) {
			isPortable = false
		}
	}

	// Check for portable temp directory
	portableTempPath := filepath.Join(portableDataPath, "tmp")
	isTempPortable := isPortable
	if isTempPortable {
		if _, err := os.Stat(portableTempPath); os.IsNotExist(err) {
			isTempPortable = false
		}
	}

	// Set environment variables based on portable mode
	if isPortable {
		os.Setenv("VSCODE_PORTABLE", portableDataPath)
	} else {
		os.Unsetenv("VSCODE_PORTABLE")
	}

	// Set temporary directory for portable mode
	if isTempPortable {
		switch runtime.GOOS {
		case "windows":
			os.Setenv("TMP", portableTempPath)
			os.Setenv("TEMP", portableTempPath)
		default:
			os.Setenv("TMPDIR", portableTempPath)
		}
	}

	return PortableConfiguration{
		PortableDataPath: portableDataPath,
		IsPortable:       isPortable,
	}
}
