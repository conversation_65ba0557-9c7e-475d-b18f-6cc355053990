/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package vscode_go

import (
	"testing"

	"github.com/Xuanwo/go-locale"
)

// TestGoLocaleIntegration tests the Xuanwo/go-locale library integration
func TestGoLocaleIntegration(t *testing.T) {
	// Test direct go-locale functionality - primary locale
	primaryTag, err := locale.Detect()
	if err != nil {
		t.Logf("Warning: Failed to detect primary system locale: %v", err)
		// This is not necessarily a failure, as some systems might not support this
	} else {
		t.Logf("Primary system locale: %s", primaryTag.String())
	}

	// Test all locales detection
	allTags, err := locale.DetectAll()
	if err != nil {
		t.Logf("Warning: Failed to get all system locales: %v", err)
		// This is not necessarily a failure, as some systems might not support this
		return
	}

	// Convert tags to strings for logging
	locales := make([]string, len(allTags))
	for i, tag := range allTags {
		locales[i] = tag.String()
	}

	t.Logf("Detected system locales: %v", locales)

	if len(allTags) == 0 {
		t.Log("No system locales detected, this might be expected on some systems")
		return
	}

	// Verify we got at least one locale
	if len(allTags) > 0 {
		t.Logf("Primary system locale from DetectAll: %s", allTags[0].String())
	}
}

// TestVSCodeServiceLocaleIntegration tests the VSCode service locale integration
func TestVSCodeServiceLocaleIntegration(t *testing.T) {
	service, err := NewVSCodeService()
	if err != nil {
		t.Fatalf("Failed to create VSCode service: %v", err)
	}
	defer service.Shutdown()

	// Test system locales detection
	systemLocales := service.GetSystemLocales()
	t.Logf("VSCode Service - System locales: %v", systemLocales)

	// Test preferred system languages
	systemLanguages := service.GetPreferredSystemLanguages()
	t.Logf("VSCode Service - System languages: %v", systemLanguages)

	// Test comprehensive locale info
	localeInfo := service.GetLocaleInfo()
	t.Logf("VSCode Service - Locale info: %+v", localeInfo)

	// Verify we have at least English as fallback
	if len(systemLanguages) == 0 {
		t.Error("Expected at least one system language (should include 'en' as fallback)")
	}

	// Check if English is available as fallback
	hasEnglish := false
	for _, lang := range systemLanguages {
		if lang == "en" {
			hasEnglish = true
			break
		}
	}

	if !hasEnglish {
		t.Error("Expected 'en' to be available as fallback language")
	}
}

// TestProcessZhLocale tests the Chinese locale processing
func TestProcessZhLocale(t *testing.T) {
	service, err := NewVSCodeService()
	if err != nil {
		t.Fatalf("Failed to create VSCode service: %v", err)
	}
	defer service.Shutdown()

	testCases := []struct {
		input    string
		expected string
	}{
		{"zh-hans", "zh-cn"},
		{"zh-cn", "zh-cn"},
		{"zh-sg", "zh-cn"},
		{"zh-my", "zh-cn"},
		{"zh-hant", "zh-tw"},
		{"zh-tw", "zh-tw"},
		{"zh-hk", "zh-tw"},
		{"en", "en"},
		{"fr", "fr"},
		{"", ""},
	}

	for _, tc := range testCases {
		result := service.processZhLocale(tc.input)
		if result != tc.expected {
			t.Errorf("processZhLocale(%q) = %q, expected %q", tc.input, result, tc.expected)
		}
	}
}

// BenchmarkLocaleDetection benchmarks the locale detection performance
func BenchmarkLocaleDetection(b *testing.B) {
	service, err := NewVSCodeService()
	if err != nil {
		b.Fatalf("Failed to create VSCode service: %v", err)
	}
	defer service.Shutdown()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.getSystemLocales()
	}
}

// BenchmarkLanguageProcessing benchmarks the language processing performance
func BenchmarkLanguageProcessing(b *testing.B) {
	service, err := NewVSCodeService()
	if err != nil {
		b.Fatalf("Failed to create VSCode service: %v", err)
	}
	defer service.Shutdown()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.getPreferredSystemLanguages()
	}
}