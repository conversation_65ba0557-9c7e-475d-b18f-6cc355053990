/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package vscode_go

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
)

// INLSConfiguration represents the NLS configuration (simplified)
type INLSConfiguration struct {
	UserLocale          string            `json:"userLocale"`
	OSLocale            string            `json:"osLocale"`
	ResolvedLanguage    string            `json:"resolvedLanguage"`
	DefaultMessagesFile string            `json:"defaultMessagesFile"`
	Locale              string            `json:"locale"`
	AvailableLanguages  map[string]string `json:"availableLanguages"`
}

// IProductConfiguration represents the product configuration (simplified)
type IProductConfiguration struct {
	NameShort string  `json:"nameShort"`
	Commit    *string `json:"commit,omitempty"`
}

// VSCodeService provides VS Code backend functionality as a Wails v3 service
// Converted from the original vscode_go/main.go standalone application
type VSCodeService struct {
	// VS Code configuration (converted from global variables)
	dirname                   string
	portable                  PortableConfiguration
	userDataPath              string
	codeCachePath             string
	nlsConfigurationPromise   *INLSConfiguration
	nlsConfigurationPromiseMu sync.Mutex
	product                   *IProductConfiguration

	// Service state for Wails v3
	initialized bool
	ctx         context.Context
	cancel      context.CancelFunc
	mutex       sync.RWMutex
}

// NewVSCodeService creates a new VS Code service instance
func NewVSCodeService() (*VSCodeService, error) {
	ctx, cancel := context.WithCancel(context.Background())

	service := &VSCodeService{
		ctx:    ctx,
		cancel: cancel,
	}

	// Initialize dirname (equivalent to init() function)
	if execPath, err := os.Executable(); err == nil {
		service.dirname = filepath.Dir(execPath)
	} else {
		cancel()
		return nil, fmt.Errorf("failed to get executable path: %w", err)
	}

	// Initialize the service (equivalent to main() function)
	if err := service.initialize(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize VS Code service: %w", err)
	}

	return service, nil
}

// initialize sets up the VS Code service (converted from main() function)
func (vs *VSCodeService) initialize() error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	log.Println("🚀 Initializing VS Code service...")

	// Initialize product configuration
	vs.product = &IProductConfiguration{
		NameShort: "Kawai Agent VS Code",
		Commit:    stringPtr("dev"),
	}

	// Enable portable support
	vs.portable = vs.configurePortable(vs.product)

	// Set userData path before app 'ready' event
	productNameShort := "kawai-agent"
	if vs.product != nil && vs.product.NameShort != "" {
		productNameShort = vs.product.NameShort
	}

	var err error
	vs.userDataPath, err = vs.getUserDataPath(productNameShort)
	if err != nil {
		return fmt.Errorf("error getting user data path: %w", err)
	}

	// Handle UNC paths on Windows
	if runtime.GOOS == "windows" {
		userDataUNCHost := vs.getUNCHost(vs.userDataPath)
		if userDataUNCHost != "" {
			vs.addUNCHostToAllowlist(userDataUNCHost) // enables to use UNC paths in userDataPath
		}
	}

	// Resolve code cache path
	vs.codeCachePath = vs.getCodeCachePath()

	// Set logs path before app 'ready' event if running portable
	// to ensure that no 'logs' folder is created on disk at a
	// location outside of the portable directory
	if vs.portable.IsPortable {
		// In a real Electron app, this would be app.setAppLogsPath()
		// For Go, we'll set an environment variable
		logsPath := filepath.Join(vs.userDataPath, "logs")
		os.Setenv("VSCODE_LOGS_PATH", logsPath)
	}

	// We can resolve the NLS configuration early if it is defined
	// in argv.json before `app.ready` event. Otherwise we can only
	// resolve NLS after `app.ready` event to resolve the OS locale.

	// Use the most preferred OS language for language recommendation.
	// The API might return an empty array on Linux, such as when
	// the 'C' locale is the user's only configured locale.
	// No matter the OS, if the array is empty, default back to 'en'.
	osLocale := vs.processZhLocale(vs.getPreferredSystemLanguage())
	userLocale := vs.getUserDefinedLocale()

	if userLocale != "" {
		vs.nlsConfigurationPromise = vs.resolveNLSConfigurationEarly(userLocale, osLocale)
	}

	// Pass in the locale to the application
	// For now, don't pass in the locale on macOS due to potential issues
	// If the locale is `qps-ploc`, the Microsoft
	// Pseudo Language Language Pack is being used.
	// In that case, use `en` as the locale.
	if runtime.GOOS == "windows" || runtime.GOOS == "linux" {
		electronLocale := "en"
		if userLocale != "" && userLocale != "qps-ploc" {
			electronLocale = userLocale
		}
		// In a real Electron app, this would be app.commandLine.appendSwitch('lang', electronLocale)
		os.Setenv("VSCODE_LOCALE", electronLocale)
	}

	// Load our code once ready
	if err := vs.onReady(); err != nil {
		return fmt.Errorf("error during ready phase: %w", err)
	}

	vs.initialized = true
	log.Println("✅ VS Code service initialized successfully")

	return nil
}

// onReady handles the application ready event
func (vs *VSCodeService) onReady() error {
	// Create code cache directory if needed
	vs.mkdirpIgnoreError(vs.codeCachePath)

	// Resolve NLS configuration
	nlsConfig, err := vs.resolveNlsConfiguration()
	if err != nil {
		log.Printf("Error resolving NLS configuration: %v", err)
		// Continue with default configuration
		nlsConfig = &INLSConfiguration{
			UserLocale:          "en",
			OSLocale:            vs.getPreferredSystemLanguage(),
			ResolvedLanguage:    "en",
			DefaultMessagesFile: filepath.Join(vs.dirname, "nls.messages.json"),
			Locale:              "en",
			AvailableLanguages:  map[string]string{},
		}
	}

	// Start the application
	return vs.startup(vs.codeCachePath, nlsConfig)
}

// startup is the main startup routine
func (vs *VSCodeService) startup(codeCachePath string, nlsConfig *INLSConfiguration) error {
	// Set environment variables
	nlsConfigJSON, _ := json.Marshal(nlsConfig)
	os.Setenv("VSCODE_NLS_CONFIG", string(nlsConfigJSON))

	if codeCachePath != "" {
		os.Setenv("VSCODE_CODE_CACHE_PATH", codeCachePath)
	} else {
		os.Setenv("VSCODE_CODE_CACHE_PATH", "")
	}

	// Bootstrap ESM
	if err := vs.bootstrapESM(); err != nil {
		return fmt.Errorf("error bootstrapping ESM: %v", err)
	}

	// In the TypeScript version, this would load the main Electron module
	// For Go, we'll just log that we've reached this point
	log.Println("✅ VS Code startup complete")
	log.Printf("📁 User data path: %s", vs.userDataPath)
	log.Printf("💾 Code cache path: %s", vs.codeCachePath)
	log.Printf("📦 Portable mode: %t", vs.portable.IsPortable)
	log.Printf("🌐 NLS language: %s", nlsConfig.ResolvedLanguage)

	return nil
}

// getCodeCachePath determines the code cache path
func (vs *VSCodeService) getCodeCachePath() string {
	// Check command line arguments for --no-cached-data
	for _, arg := range os.Args {
		if arg == "--no-cached-data" {
			return ""
		}
	}

	// Running out of sources
	if os.Getenv("VSCODE_DEV") != "" {
		return ""
	}

	// Require commit id
	if product == nil || product.Commit == nil || *product.Commit == "" {
		return ""
	}

	return filepath.Join(userDataPath, "CachedData", *product.Commit)
}

// mkdirpIgnoreError creates a directory recursively, ignoring errors
func (vs *VSCodeService) mkdirpIgnoreError(dir string) {
	if dir != "" {
		os.MkdirAll(dir, 0755)
	}
}

// processZhLocale processes Chinese locale codes
func (vs *VSCodeService) processZhLocale(appLocale string) string {
	if strings.HasPrefix(appLocale, "zh") {
		parts := strings.Split(appLocale, "-")
		if len(parts) > 1 {
			region := parts[1]

			// On Windows and macOS, Chinese languages returned by
			// app.getPreferredSystemLanguages() start with zh-hans
			// for Simplified Chinese or zh-hant for Traditional Chinese,
			// so we can easily determine whether to use Simplified or Traditional.
			// However, on Linux, Chinese languages returned by that same API
			// are of the form zh-XY, where XY is a country code.
			// For China (CN), Singapore (SG), and Malaysia (MY)
			// country codes, assume they use Simplified Chinese.
			// For other cases, assume they use Traditional.
			simplifiedRegions := []string{"hans", "cn", "sg", "my"}
			for _, r := range simplifiedRegions {
				if strings.EqualFold(region, r) {
					return "zh-cn"
				}
			}

			return "zh-tw"
		}
	}

	return appLocale
}

// getPreferredSystemLanguage gets the preferred system language
func (vs *VSCodeService) getPreferredSystemLanguage() string {
	// This is a simplified implementation
	// In a real implementation, you'd use OS-specific APIs
	locale := os.Getenv("LANG")
	if locale == "" {
		locale = "en"
	}

	// Extract language code from locale (e.g., "en_US.UTF-8" -> "en")
	if idx := strings.Index(locale, "_"); idx != -1 {
		locale = locale[:idx]
	}
	if idx := strings.Index(locale, "."); idx != -1 {
		locale = locale[:idx]
	}

	return strings.ToLower(locale)
}

// getUserDefinedLocale gets the user-defined locale
func (vs *VSCodeService) getUserDefinedLocale() string {
	// TODO: support --locale command line argument
	// For now, return empty string
	return ""
}

// resolveNLSConfigurationEarly resolves NLS configuration early
func (vs *VSCodeService) resolveNLSConfigurationEarly(userLocale, osLocale string) *INLSConfiguration {
	// Simplified NLS configuration resolution
	return &INLSConfiguration{
		UserLocale:          userLocale,
		OSLocale:            osLocale,
		ResolvedLanguage:    userLocale,
		DefaultMessagesFile: filepath.Join(dirname, "nls.messages.json"),
		Locale:              userLocale,
		AvailableLanguages:  map[string]string{"*": userLocale},
	}
}

// resolveNlsConfiguration resolves the NLS configuration
func (vs *VSCodeService) resolveNlsConfiguration() (*INLSConfiguration, error) {
	vs.nlsConfigurationPromiseMu.Lock()
	defer vs.nlsConfigurationPromiseMu.Unlock()

	// First, we need to test a user defined locale.
	// If it fails we try the app locale.
	// If that fails we fall back to English.

	if vs.nlsConfigurationPromise != nil {
		return vs.nlsConfigurationPromise, nil
	}

	// Try to use the app locale
	userLocale := vs.getPreferredSystemLanguage()
	if userLocale == "" {
		return &INLSConfiguration{
			UserLocale:          "en",
			OSLocale:            vs.getPreferredSystemLanguage(),
			ResolvedLanguage:    "en",
			DefaultMessagesFile: filepath.Join(vs.dirname, "nls.messages.json"),
			Locale:              "en",
			AvailableLanguages:  map[string]string{},
		}, nil
	}

	// See above the comment about the loader and case sensitiveness
	userLocale = vs.processZhLocale(strings.ToLower(userLocale))

	return vs.resolveNLSConfigurationEarly(userLocale, vs.getPreferredSystemLanguage()), nil
}

// Helper functions

// stringPtr returns a pointer to a string
func stringPtr(s string) *string {
	return &s
}

// configurePortable configures portable mode
func (vs *VSCodeService) configurePortable(product *IProductConfiguration) PortableConfiguration {
	// Simplified portable configuration
	portableDataPath := ""
	isPortable := false

	if portablePath := os.Getenv("VSCODE_PORTABLE"); portablePath != "" {
		portableDataPath = portablePath
		isPortable = true
	}

	return PortableConfiguration{
		PortableDataPath: portableDataPath,
		IsPortable:       isPortable,
	}
}

// getUserDataPath gets the user data path
func (vs *VSCodeService) getUserDataPath(productName string) (string, error) {
	// Simplified user data path resolution
	if os.Getenv("VSCODE_DEV") != "" {
		productName = "code-oss-dev"
	}

	// Support portable mode
	if portablePath := os.Getenv("VSCODE_PORTABLE"); portablePath != "" {
		return filepath.Join(portablePath, "user-data"), nil
	}

	// Support global VSCODE_APPDATA environment variable
	if appDataPath := os.Getenv("VSCODE_APPDATA"); appDataPath != "" {
		return filepath.Join(appDataPath, productName), nil
	}

	// Platform-specific paths
	var appDataPath string
	switch runtime.GOOS {
	case "windows":
		appDataPath = os.Getenv("APPDATA")
		if appDataPath == "" {
			userProfile := os.Getenv("USERPROFILE")
			if userProfile == "" {
				return "", fmt.Errorf("Windows: Unexpected undefined %%USERPROFILE%% environment variable")
			}
			appDataPath = filepath.Join(userProfile, "AppData", "Roaming")
		}
	case "darwin":
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		appDataPath = filepath.Join(homeDir, "Library", "Application Support")
	case "linux":
		xdgConfigHome := os.Getenv("XDG_CONFIG_HOME")
		if xdgConfigHome != "" {
			appDataPath = xdgConfigHome
		} else {
			homeDir, err := os.UserHomeDir()
			if err != nil {
				return "", err
			}
			appDataPath = filepath.Join(homeDir, ".config")
		}
	default:
		return "", fmt.Errorf("Platform not supported")
	}

	return filepath.Join(appDataPath, productName), nil
}

// getUNCHost extracts UNC host from path (Windows only)
func (vs *VSCodeService) getUNCHost(maybeUNCPath string) string {
	if runtime.GOOS != "windows" || maybeUNCPath == "" {
		return ""
	}

	uncRoots := []string{
		"\\\\.\\UNC\\",
		"\\\\?\\UNC\\",
		"\\\\",
	}

	for _, uncRoot := range uncRoots {
		if !strings.HasPrefix(maybeUNCPath, uncRoot) {
			continue
		}

		pathAfterRoot := maybeUNCPath[len(uncRoot):]
		indexOfNextBackslash := strings.Index(pathAfterRoot, "\\")

		if indexOfNextBackslash == -1 {
			continue
		}

		hostCandidate := pathAfterRoot[:indexOfNextBackslash]
		if hostCandidate != "" {
			return hostCandidate
		}
	}

	return ""
}

// addUNCHostToAllowlist adds UNC host to allowlist (Windows only)
func (vs *VSCodeService) addUNCHostToAllowlist(host string) {
	if runtime.GOOS != "windows" || host == "" {
		return
	}
	// In a real implementation, this would interact with the process UNC allowlist
	// For now, we'll just log it
	log.Printf("Adding UNC host to allowlist: %s", host)
}

// bootstrapESM bootstraps the ESM system
func (vs *VSCodeService) bootstrapESM() error {
	// Simplified ESM bootstrap
	log.Println("🔧 Bootstrapping ESM...")
	return nil
}

// Wails v3 Service Interface Methods

// Name returns the service name
func (vs *VSCodeService) Name() string {
	return "VSCodeService"
}

// Shutdown gracefully shuts down the VS Code service
func (vs *VSCodeService) Shutdown() error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	if !vs.initialized {
		return nil
	}

	log.Println("🔄 Shutting down VS Code service...")

	vs.cancel()
	vs.initialized = false

	log.Println("✅ VS Code service shut down successfully")
	return nil
}

// IsInitialized returns whether the service is initialized
func (vs *VSCodeService) IsInitialized() bool {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.initialized
}

// Context returns the service context
func (vs *VSCodeService) Context() context.Context {
	return vs.ctx
}

// Wails v3 Frontend Binding Methods
// These methods will be automatically exposed to the frontend via Wails bindings

// GetVSCodeInfo returns VS Code configuration information for the frontend
func (vs *VSCodeService) GetVSCodeInfo() map[string]interface{} {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()

	if !vs.initialized {
		return map[string]interface{}{
			"initialized": false,
			"error":       "VS Code service not initialized",
		}
	}

	info := map[string]interface{}{
		"initialized":   true,
		"userDataPath":  vs.userDataPath,
		"codeCachePath": vs.codeCachePath,
		"isPortable":    vs.portable.IsPortable,
		"dirname":       vs.dirname,
	}

	if vs.product != nil {
		info["product"] = map[string]interface{}{
			"nameShort": vs.product.NameShort,
			"commit":    vs.product.Commit,
		}
	}

	if vs.nlsConfigurationPromise != nil {
		info["nls"] = vs.nlsConfigurationPromise
	}

	return info
}

// GetUserDataPath returns the VS Code user data path
func (vs *VSCodeService) GetUserDataPath() string {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.userDataPath
}

// GetCodeCachePath returns the VS Code code cache path
func (vs *VSCodeService) GetCodeCachePath() string {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.codeCachePath
}

// IsPortable returns whether VS Code is running in portable mode
func (vs *VSCodeService) IsPortable() bool {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.portable.IsPortable
}

// GetNLSConfiguration returns the current NLS configuration
func (vs *VSCodeService) GetNLSConfiguration() *INLSConfiguration {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()
	return vs.nlsConfigurationPromise
}
