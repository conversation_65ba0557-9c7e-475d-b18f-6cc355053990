/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package vscode_go

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sync"

	"github.com/yudaprama/kawai-agent/vscode_go/vs"
	base "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// Global variables for NLS and product configuration
var (
	globalProductJSON *base.IProductConfiguration
	globalPackageJSON map[string]interface{}
	globalFileRoot    string
	globalNLSLanguage *string
	globalNLSMessages []string
	setupNLSOnce      sync.Once
	setupNLSResult    *vs.INLSConfiguration
	setupNLSError     error
)

// Initialize global variables
func init() {
	// Get the executable directory as file root
	if execPath, err := os.Executable(); err == nil {
		globalFileRoot = filepath.Dir(execPath)
	}
}

// SetGlobalProductJSON sets the global product configuration
func SetGlobalProductJSON(product *base.IProductConfiguration) {
	globalProductJSON = product
}

// SetGlobalPackageJSON sets the global package configuration
func SetGlobalPackageJSON(pkg map[string]interface{}) {
	globalPackageJSON = pkg
}

// GetGlobalProductJSON returns the global product configuration
func GetGlobalProductJSON() *base.IProductConfiguration {
	return globalProductJSON
}

// GetGlobalPackageJSON returns the global package configuration
func GetGlobalPackageJSON() map[string]interface{} {
	return globalPackageJSON
}

// GetGlobalFileRoot returns the global file root
func GetGlobalFileRoot() string {
	return globalFileRoot
}

// SetGlobalFileRoot sets the global file root
func SetGlobalFileRoot(fileRoot string) {
	globalFileRoot = fileRoot
}

// setupNLS sets up NLS configuration
func setupNLS() (*vs.INLSConfiguration, error) {
	setupNLSOnce.Do(func() {
		setupNLSResult, setupNLSError = doSetupNLS()
	})
	return setupNLSResult, setupNLSError
}

// doSetupNLS performs the actual NLS setup
func doSetupNLS() (*vs.INLSConfiguration, error) {
	var nlsConfig *vs.INLSConfiguration
	var messagesFile string

	// Check if VSCODE_NLS_CONFIG environment variable is set
	if nlsConfigEnv := os.Getenv("VSCODE_NLS_CONFIG"); nlsConfigEnv != "" {
		if err := json.Unmarshal([]byte(nlsConfigEnv), &nlsConfig); err != nil {
			return nil, fmt.Errorf("error reading VSCODE_NLS_CONFIG from environment: %v", err)
		}

		if nlsConfig != nil {
			if nlsConfig.LanguagePack != nil && nlsConfig.LanguagePack.MessagesFile != "" {
				messagesFile = nlsConfig.LanguagePack.MessagesFile
			} else if nlsConfig.DefaultMessagesFile != "" {
				messagesFile = nlsConfig.DefaultMessagesFile
			}

			// Set global NLS language
			globalNLSLanguage = &nlsConfig.ResolvedLanguage
			vs.SetNLSLanguage(globalNLSLanguage)
		}
	}

	// If in development mode or no messages file, return early
	if os.Getenv("VSCODE_DEV") != "" || messagesFile == "" {
		return nil, nil
	}

	// Read the messages file
	messagesData, err := ioutil.ReadFile(messagesFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error reading NLS messages file %s: %v\n", messagesFile, err)

		// Mark as corrupt: this will re-create the language pack cache next startup
		if nlsConfig != nil && nlsConfig.LanguagePack != nil && nlsConfig.LanguagePack.CorruptMarkerFile != "" {
			if writeErr := ioutil.WriteFile(nlsConfig.LanguagePack.CorruptMarkerFile, []byte("corrupted"), 0644); writeErr != nil {
				fmt.Fprintf(os.Stderr, "Error writing corrupted NLS marker file: %v\n", writeErr)
			}
		}

		// Fallback to the default message file to ensure english translation at least
		if nlsConfig != nil && nlsConfig.DefaultMessagesFile != "" && nlsConfig.DefaultMessagesFile != messagesFile {
			if fallbackData, fallbackErr := ioutil.ReadFile(nlsConfig.DefaultMessagesFile); fallbackErr == nil {
				messagesData = fallbackData
			} else {
				fmt.Fprintf(os.Stderr, "Error reading default NLS messages file %s: %v\n", nlsConfig.DefaultMessagesFile, fallbackErr)
			}
		}
	}

	// Parse messages if we have data
	if len(messagesData) > 0 {
		var messages []string
		if err := json.Unmarshal(messagesData, &messages); err != nil {
			return nil, fmt.Errorf("error parsing NLS messages: %v", err)
		}
		globalNLSMessages = messages
		vs.SetNLSMessages(messages)
	}

	return nlsConfig, nil
}

// BootstrapESM initializes the ESM bootstrap process
func BootstrapESM() error {
	// Setup NLS
	_, err := setupNLS()
	if err != nil {
		return fmt.Errorf("error setting up NLS: %v", err)
	}

	return nil
}

// PrepareGlobals prepares the global variables that are needed for running
func PrepareGlobals(product *base.IProductConfiguration, pkg map[string]interface{}) {
	// Set global product configuration
	if product != nil {
		globalProductJSON = product

		// In development mode, try to load product overrides
		if os.Getenv("VSCODE_DEV") != "" {
			if overridesData, err := ioutil.ReadFile("../product.overrides.json"); err == nil {
				var overrides map[string]interface{}
				if json.Unmarshal(overridesData, &overrides) == nil {
					// Merge overrides into product configuration
					// This is a simplified merge - in a real implementation you'd want
					// a more sophisticated merge strategy
					productBytes, _ := json.Marshal(globalProductJSON)
					var productMap map[string]interface{}
					json.Unmarshal(productBytes, &productMap)

					for k, v := range overrides {
						productMap[k] = v
					}

					mergedBytes, _ := json.Marshal(productMap)
					json.Unmarshal(mergedBytes, &globalProductJSON)
				}
			}
		}
	}

	// Set global package configuration
	if pkg != nil {
		globalPackageJSON = pkg
	}

	// Set global file root
	if execPath, err := os.Executable(); err == nil {
		globalFileRoot = filepath.Dir(execPath)
	}
}
