/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"sync"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformLog "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// Choose a buffer size that is a balance between memory needs and
// manageable IPC overhead. The larger the buffer size, the less
// roundtrips we have to do for reading/writing data.
const BUFFER_SIZE = 256 * 1024

// FileService implements IFileService and provides file system operations
type FileService struct {
	baseCommon.Disposable

	// Core dependencies
	logService platformLog.ILogService

	// Provider management
	providerMutex sync.RWMutex
	providers     map[string]IFileSystemProvider

	// Events
	onDidChangeFileSystemProviderRegistrationsEmitter *baseCommon.Emitter[IFileSystemProviderRegistrationEvent]
	onDidRunOperationEmitter                          *baseCommon.Emitter[IFileOperationEvent]
	onDidFilesChangeEmitter                           *baseCommon.Emitter[*FileChangesEvent]
	onDidWatchErrorEmitter                            *baseCommon.Emitter[error]

	// Active watchers
	activeWatchersMutex sync.Mutex
	activeWatchers      map[string]*activeWatcher

	// Extension URI utilities cache
	extUriMutex sync.RWMutex
	extUriCache map[IFileSystemProvider]*extUriInfo
}

// activeWatcher tracks an active file system watcher
type activeWatcher struct {
	disposable baseCommon.IDisposable
	count      int
}

// extUriInfo caches extension URI information for a provider
type extUriInfo struct {
	providerExtUri      baseCommon.IExtUri
	isPathCaseSensitive bool
}

// NewFileService creates a new file service instance
func NewFileService(logService platformLog.ILogService) *FileService {
	fs := &FileService{
		logService:     logService,
		providers:      make(map[string]IFileSystemProvider),
		activeWatchers: make(map[string]*activeWatcher),
		extUriCache:    make(map[IFileSystemProvider]*extUriInfo),
	}

	// Initialize emitters
	fs.onDidChangeFileSystemProviderRegistrationsEmitter = baseCommon.NewEmitter[IFileSystemProviderRegistrationEvent]()
	fs.onDidRunOperationEmitter = baseCommon.NewEmitter[IFileOperationEvent]()
	fs.onDidFilesChangeEmitter = baseCommon.NewEmitter[*FileChangesEvent]()
	fs.onDidWatchErrorEmitter = baseCommon.NewEmitter[error]()

	return fs
}

// Event accessors
func (fs *FileService) OnDidChangeFileSystemProviderRegistrations() baseCommon.Event[IFileSystemProviderRegistrationEvent] {
	return fs.onDidChangeFileSystemProviderRegistrationsEmitter.Event()
}

func (fs *FileService) OnDidRunOperation() baseCommon.Event[IFileOperationEvent] {
	return fs.onDidRunOperationEmitter.Event()
}

func (fs *FileService) OnDidFilesChange() baseCommon.Event[*FileChangesEvent] {
	return fs.onDidFilesChangeEmitter.Event()
}

func (fs *FileService) OnDidWatchError() baseCommon.Event[error] {
	return fs.onDidWatchErrorEmitter.Event()
}

// RegisterProvider registers a file system provider for a scheme
func (fs *FileService) RegisterProvider(scheme string, provider IFileSystemProvider) baseCommon.IDisposable {
	fs.providerMutex.Lock()
	defer fs.providerMutex.Unlock()

	if _, exists := fs.providers[scheme]; exists {
		fs.logService.Error(fmt.Sprintf("A file system provider for the scheme '%s' is already registered", scheme))
		return baseCommon.ToDisposable(func() {})
	}

	// Register the provider
	fs.providers[scheme] = provider

	// Clear extension URI cache for this provider
	fs.extUriMutex.Lock()
	delete(fs.extUriCache, provider)
	fs.extUriMutex.Unlock()

	// Fire registration event
	fs.onDidChangeFileSystemProviderRegistrationsEmitter.Fire(IFileSystemProviderRegistrationEvent{
		Added:    true,
		Scheme:   scheme,
		Provider: provider,
	})

	return baseCommon.ToDisposable(func() {
		fs.providerMutex.Lock()
		defer fs.providerMutex.Unlock()

		delete(fs.providers, scheme)

		fs.extUriMutex.Lock()
		delete(fs.extUriCache, provider)
		fs.extUriMutex.Unlock()

		// Fire registration event
		fs.onDidChangeFileSystemProviderRegistrationsEmitter.Fire(IFileSystemProviderRegistrationEvent{
			Added:    false,
			Scheme:   scheme,
			Provider: provider,
		})
	})
}

// GetProvider returns the file system provider for a scheme
func (fs *FileService) GetProvider(scheme string) IFileSystemProvider {
	fs.providerMutex.RLock()
	defer fs.providerMutex.RUnlock()

	return fs.providers[scheme]
}

// HasProvider checks if a provider exists for the given resource
func (fs *FileService) HasProvider(resource *baseCommon.URI) bool {
	fs.providerMutex.RLock()
	defer fs.providerMutex.RUnlock()

	_, exists := fs.providers[resource.Scheme]
	return exists
}

// HasCapability checks if a provider has a specific capability
func (fs *FileService) HasCapability(resource *baseCommon.URI, capability FileSystemProviderCapabilities) bool {
	provider := fs.GetProvider(resource.Scheme)
	if provider == nil {
		return false
	}

	return (provider.GetCapabilities() & capability) != 0
}

// ListCapabilities returns a list of all provider capabilities
func (fs *FileService) ListCapabilities() []ProviderCapability {
	fs.providerMutex.RLock()
	defer fs.providerMutex.RUnlock()

	capabilities := make([]ProviderCapability, 0, len(fs.providers))
	for scheme, provider := range fs.providers {
		capabilities = append(capabilities, ProviderCapability{
			Scheme:       scheme,
			Capabilities: provider.GetCapabilities(),
		})
	}

	return capabilities
}

// ProviderCapability represents a provider's scheme and capabilities
type ProviderCapability struct {
	Scheme       string
	Capabilities FileSystemProviderCapabilities
}

// withProvider gets a provider for a resource
func (fs *FileService) withProvider(resource *baseCommon.URI) (IFileSystemProvider, error) {
	provider := fs.GetProvider(resource.Scheme)
	if provider == nil {
		return nil, CreateFileSystemProviderError(
			fmt.Sprintf("No file system provider found for resource '%s'", resource.ToString()),
			FileSystemProviderErrorCodeFileNotFound,
		)
	}

	return provider, nil
}

// Exists checks if a file or folder exists
func (fs *FileService) Exists(resource *baseCommon.URI) (bool, error) {
	provider, err := fs.withProvider(resource)
	if err != nil {
		return false, nil // No provider means it doesn't exist
	}

	_, err = provider.Stat(resource)
	return err == nil, nil
}

// Resolve resolves a file or folder
func (fs *FileService) Resolve(resource *baseCommon.URI, options *IResolveFileOptions) (IFileStat, error) {
	provider, err := fs.withProvider(resource)
	if err != nil {
		return IFileStat{}, err
	}

	// Get the stat from provider
	stat, err := provider.Stat(resource)
	if err != nil {
		return IFileStat{}, EnsureFileSystemProviderError(err)
	}

	// Convert to file stat
	return fs.toFileStat(provider, resource, *stat, nil, false, nil)
}

// toFileStat converts provider stat to IFileStat
func (fs *FileService) toFileStat(
	provider IFileSystemProvider,
	resource *baseCommon.URI,
	stat IStat,
	siblings *int,
	resolveMetadata bool,
	recurse func(IFileStat, int) bool,
) (IFileStat, error) {
	readonly := stat.Permissions != nil && *stat.Permissions&FilePermissionReadonly != 0
	locked := stat.Permissions != nil && *stat.Permissions&FilePermissionLocked != 0
	etag := EtagFromStat(stat)

	// Create basic file stat
	fileStat := &IFileStat{
		IBaseFileStat: IBaseFileStat{
			Resource: resource,
			Name:     resource.Path, // Simplified for now
			Mtime:    &stat.Mtime,
			Ctime:    &stat.Ctime,
			Size:     &stat.Size,
			Readonly: &readonly,
			Locked:   &locked,
			Etag:     &etag,
		},
		IsFile:         (stat.Type & FileTypeFile) != 0,
		IsDirectory:    (stat.Type & FileTypeDirectory) != 0,
		IsSymbolicLink: (stat.Type & FileTypeSymbolicLink) != 0,
		Children:       make([]IFileStat, 0),
	}

	return *fileStat, nil
}

// Dispose cleans up the file service
func (fs *FileService) Dispose() {
	fs.Disposable.Dispose()

	// Dispose all active watchers
	fs.activeWatchersMutex.Lock()
	for _, watcher := range fs.activeWatchers {
		watcher.disposable.Dispose()
	}
	fs.activeWatchers = make(map[string]*activeWatcher)
	fs.activeWatchersMutex.Unlock()

	// Clear providers
	fs.providerMutex.Lock()
	fs.providers = make(map[string]IFileSystemProvider)
	fs.providerMutex.Unlock()

	// Clear caches
	fs.extUriMutex.Lock()
	fs.extUriCache = make(map[IFileSystemProvider]*extUriInfo)
	fs.extUriMutex.Unlock()
}

// FileStat implements IFileStat interface
type FileStat struct {
	resource       *baseCommon.URI
	name           string
	isFile         bool
	isDirectory    bool
	isSymbolicLink bool
	mtime          int64
	ctime          int64
	size           *int64
	readonly       bool
	locked         bool
	etag           string
	children       []IFileStat
}

func (fs *FileStat) Resource() *baseCommon.URI { return fs.resource }
func (fs *FileStat) Name() string              { return fs.name }
func (fs *FileStat) IsFile() bool              { return fs.isFile }
func (fs *FileStat) IsDirectory() bool         { return fs.isDirectory }
func (fs *FileStat) IsSymbolicLink() bool      { return fs.isSymbolicLink }
func (fs *FileStat) Mtime() int64              { return fs.mtime }
func (fs *FileStat) Ctime() int64              { return fs.ctime }
func (fs *FileStat) Size() *int64              { return fs.size }
func (fs *FileStat) Readonly() bool            { return fs.readonly }
func (fs *FileStat) Locked() bool              { return fs.locked }
func (fs *FileStat) Etag() string              { return fs.etag }
func (fs *FileStat) Children() []IFileStat     { return fs.children }

// ActivateProvider activates a file system provider for the given scheme
// This is required by the IFileService interface
func (fs *FileService) ActivateProvider(scheme string) error {
	// In a complete implementation, this would activate lazy providers
	// For now, we assume providers are already active when registered
	if _, exists := fs.providers[scheme]; exists {
		return nil
	}
	return fmt.Errorf("no provider registered for scheme: %s", scheme)
}

// CanCopy validates if a copy operation can be performed
// This is required by the IFileService interface
func (fs *FileService) CanCopy(source, target *baseCommon.URI, overwrite bool) error {
	// Basic validation - in a complete implementation this would check permissions, disk space, etc.
	if source == nil || target == nil {
		return fmt.Errorf("source and target must be provided")
	}

	// Check if source exists
	exists, err := fs.Exists(source)
	if err != nil {
		return fmt.Errorf("failed to check source existence: %w", err)
	}
	if !exists {
		return fmt.Errorf("source does not exist: %s", source.ToString())
	}

	// Check if target exists and overwrite is not allowed
	if !overwrite {
		targetExists, err := fs.Exists(target)
		if err != nil {
			return fmt.Errorf("failed to check target existence: %w", err)
		}
		if targetExists {
			return fmt.Errorf("target already exists and overwrite is not allowed: %s", target.ToString())
		}
	}

	return nil
}

// CanCreateFile validates if a file creation operation can be performed
// This is required by the IFileService interface
func (fs *FileService) CanCreateFile(resource *baseCommon.URI, options *ICreateFileOptions) error {
	// Basic validation - in a complete implementation this would check permissions, parent directory existence, etc.
	if resource == nil {
		return fmt.Errorf("resource must be provided")
	}

	// Check if file already exists
	exists, err := fs.Exists(resource)
	if err != nil {
		return fmt.Errorf("failed to check file existence: %w", err)
	}

	// Check overwrite option
	if exists && (options == nil || options.Overwrite == nil || !*options.Overwrite) {
		return fmt.Errorf("file already exists and overwrite is not allowed: %s", resource.ToString())
	}

	return nil
}

// CanDelete validates if a delete operation can be performed
// This is required by the IFileService interface
func (fs *FileService) CanDelete(resource *baseCommon.URI, options *IFileDeleteOptions) error {
	// Basic validation - in a complete implementation this would check permissions, locks, etc.
	if resource == nil {
		return fmt.Errorf("resource must be provided")
	}

	// Check if file/folder exists
	exists, err := fs.Exists(resource)
	if err != nil {
		return fmt.Errorf("failed to check resource existence: %w", err)
	}
	if !exists {
		return fmt.Errorf("resource does not exist: %s", resource.ToString())
	}

	return nil
}

// EtagFromStat creates an etag from file stat
func EtagFromStat(stat IStat) string {
	if stat.Mtime != 0 && stat.Size != 0 {
		return fmt.Sprintf("%d-%d", stat.Mtime, stat.Size)
	}
	return ""
}
