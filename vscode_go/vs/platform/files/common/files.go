/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"context"
	"errors"
	"fmt"
	"strings"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformInstantiation "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// FileType represents the type of a file system entry
type FileType int

const (
	FileTypeUnknown      FileType = 0  // File is unknown
	FileTypeFile         FileType = 1  // File is a normal file
	FileTypeDirectory    FileType = 2  // File is a directory
	FileTypeSymbolicLink FileType = 64 // File is a symbolic link
)

// FilePermission represents file permissions
type FilePermission int

const (
	FilePermissionReadonly FilePermission = 1 // File is readonly
	FilePermissionLocked   FilePermission = 2 // File is locked
)

// FileSystemProviderCapabilities represents capabilities of file system providers
type FileSystemProviderCapabilities int

const (
	FileSystemProviderCapabilitiesNone                   FileSystemProviderCapabilities = 0
	FileSystemProviderCapabilitiesFileReadWrite          FileSystemProviderCapabilities = 1 << 1
	FileSystemProviderCapabilitiesFileOpenReadWriteClose FileSystemProviderCapabilities = 1 << 2
	FileSystemProviderCapabilitiesFileFolderCopy         FileSystemProviderCapabilities = 1 << 3
	FileSystemProviderCapabilitiesFileReadStream         FileSystemProviderCapabilities = 1 << 4
	FileSystemProviderCapabilitiesPathCaseSensitive      FileSystemProviderCapabilities = 1 << 10
	FileSystemProviderCapabilitiesReadonly               FileSystemProviderCapabilities = 1 << 11
	FileSystemProviderCapabilitiesTrash                  FileSystemProviderCapabilities = 1 << 12
	FileSystemProviderCapabilitiesFileWriteUnlock        FileSystemProviderCapabilities = 1 << 13
	FileSystemProviderCapabilitiesFileAtomicRead         FileSystemProviderCapabilities = 1 << 14
	FileSystemProviderCapabilitiesFileAtomicWrite        FileSystemProviderCapabilities = 1 << 15
	FileSystemProviderCapabilitiesFileAtomicDelete       FileSystemProviderCapabilities = 1 << 16
	FileSystemProviderCapabilitiesFileClone              FileSystemProviderCapabilities = 1 << 17
	FileSystemProviderCapabilitiesFileRealpath           FileSystemProviderCapabilities = 1 << 18
)

// FileSystemProviderErrorCode represents error codes from file system providers
type FileSystemProviderErrorCode string

const (
	FileSystemProviderErrorCodeFileExists              FileSystemProviderErrorCode = "EntryExists"
	FileSystemProviderErrorCodeFileNotFound            FileSystemProviderErrorCode = "EntryNotFound"
	FileSystemProviderErrorCodeFileNotADirectory       FileSystemProviderErrorCode = "EntryNotADirectory"
	FileSystemProviderErrorCodeFileIsADirectory        FileSystemProviderErrorCode = "EntryIsADirectory"
	FileSystemProviderErrorCodeFileExceedsStorageQuota FileSystemProviderErrorCode = "EntryExceedsStorageQuota"
	FileSystemProviderErrorCodeFileTooLarge            FileSystemProviderErrorCode = "EntryTooLarge"
	FileSystemProviderErrorCodeFileWriteLocked         FileSystemProviderErrorCode = "EntryWriteLocked"
	FileSystemProviderErrorCodeNoPermissions           FileSystemProviderErrorCode = "NoPermissions"
	FileSystemProviderErrorCodeUnavailable             FileSystemProviderErrorCode = "Unavailable"
	FileSystemProviderErrorCodeUnknown                 FileSystemProviderErrorCode = "Unknown"
)

// FileOperation represents file operations
type FileOperation int

const (
	FileOperationCreate FileOperation = iota
	FileOperationDelete
	FileOperationMove
	FileOperationCopy
	FileOperationWrite
)

// FileChangeType represents types of file changes
type FileChangeType int

const (
	FileChangeTypeUPDATED FileChangeType = iota
	FileChangeTypeADDED
	FileChangeTypeDELETED
)

// String returns string representation of FileChangeType
func (fct FileChangeType) String() string {
	switch fct {
	case FileChangeTypeUPDATED:
		return "UPDATED"
	case FileChangeTypeADDED:
		return "ADDED"
	case FileChangeTypeDELETED:
		return "DELETED"
	default:
		return "UNKNOWN"
	}
}

// FileChangeFilter represents filters for file change events
type FileChangeFilter int

const (
	FileChangeFilterUpdated FileChangeFilter = 1 << 1
	FileChangeFilterAdded   FileChangeFilter = 1 << 2
	FileChangeFilterDeleted FileChangeFilter = 1 << 3
)

// FileOperationResult represents results of file operations
type FileOperationResult int

const (
	FileOperationResultFileIsDirectory FileOperationResult = iota
	FileOperationResultFileNotFound
	FileOperationResultFileNotModifiedSince
	FileOperationResultFileModifiedSince
	FileOperationResultFileMoveConflict
	FileOperationResultFileWriteLocked
	FileOperationResultFilePermissionDenied
	FileOperationResultFileTooLarge
	FileOperationResultFileInvalidPath
	FileOperationResultFileNotDirectory
	FileOperationResultFileOtherError
)

// IStat represents basic file system stat information
type IStat struct {
	Type        FileType        `json:"type"`
	Mtime       int64           `json:"mtime"`       // milliseconds since unix epoch
	Ctime       int64           `json:"ctime"`       // milliseconds since unix epoch
	Size        int64           `json:"size"`        // size in bytes
	Permissions *FilePermission `json:"permissions"` // optional permissions
}

// IFileAtomicOptions represents options for atomic file operations
type IFileAtomicOptions struct {
	Postfix string `json:"postfix"` // postfix for temporary files
}

// IFileReadLimits represents limits for file reading
type IFileReadLimits struct {
	Size *int64 `json:"size"` // maximum file size
}

// IFileReadStreamOptions represents options for reading file streams
type IFileReadStreamOptions struct {
	Position *int64           `json:"position"` // start position
	Length   *int64           `json:"length"`   // number of bytes to read
	Limits   *IFileReadLimits `json:"limits"`   // size limits
}

// IFileOverwriteOptions represents overwrite options
type IFileOverwriteOptions struct {
	Overwrite bool `json:"overwrite"`
}

// IFileUnlockOptions represents unlock options
type IFileUnlockOptions struct {
	Unlock bool `json:"unlock"`
}

// IFileAtomicReadOptions represents atomic read options
type IFileAtomicReadOptions struct {
	Atomic bool `json:"atomic"`
}

// IFileAtomicWriteOptions represents atomic write options
type IFileAtomicWriteOptions struct {
	Atomic interface{} `json:"atomic"` // IFileAtomicOptions or false
}

// IFileAtomicDeleteOptions represents atomic delete options
type IFileAtomicDeleteOptions struct {
	Atomic interface{} `json:"atomic"` // IFileAtomicOptions or false
}

// IFileWriteOptions represents file write options
type IFileWriteOptions struct {
	IFileOverwriteOptions
	IFileUnlockOptions
	IFileAtomicWriteOptions
	Create bool   `json:"create"` // create file if it doesn't exist
	Mtime  *int64 `json:"mtime"`  // last modification time
	Etag   string `json:"etag"`   // etag for dirty write prevention
}

// IFileDeleteOptions represents file delete options
type IFileDeleteOptions struct {
	Recursive bool        `json:"recursive"` // delete recursively
	UseTrash  bool        `json:"useTrash"`  // move to trash instead of permanent delete
	Atomic    interface{} `json:"atomic"`    // IFileAtomicOptions or false
}

// ICreateFileOptions represents file creation options
type ICreateFileOptions struct {
	Overwrite *bool `json:"overwrite"` // overwrite if exists
}

// IResolveFileOptions represents file resolution options
type IResolveFileOptions struct {
	ResolveTo                     []*baseCommon.URI `json:"resolveTo"`                     // continue resolving until these resources are found
	ResolveSingleChildDescendants *bool             `json:"resolveSingleChildDescendants"` // continue resolving if only one child
	ResolveMetadata               *bool             `json:"resolveMetadata"`               // resolve metadata (mtime, ctime, size, etag)
}

// IResolveMetadataFileOptions represents metadata file resolution options
type IResolveMetadataFileOptions struct {
	IResolveFileOptions
	ResolveMetadata bool `json:"resolveMetadata"` // must be true
}

// IWatchOptionsWithoutCorrelation represents watch options without correlation
type IWatchOptionsWithoutCorrelation struct {
	Recursive bool              `json:"recursive"` // watch recursively
	Excludes  []string          `json:"excludes"`  // paths/patterns to exclude
	Includes  []string          `json:"includes"`  // paths/patterns to include
	Filter    *FileChangeFilter `json:"filter"`    // event filter
}

// IWatchOptions represents watch options with optional correlation
type IWatchOptions struct {
	IWatchOptionsWithoutCorrelation
	CorrelationId *int `json:"correlationId"` // correlation identifier
}

// IWatchOptionsWithCorrelation represents watch options with required correlation
type IWatchOptionsWithCorrelation struct {
	IWatchOptions
	CorrelationId int `json:"correlationId"` // required correlation identifier
}

// IBaseReadFileOptions represents base options for reading files
type IBaseReadFileOptions struct {
	IFileReadStreamOptions
	Etag string `json:"etag"` // etag for early return
}

// IReadFileOptions represents options for reading files
type IReadFileOptions struct {
	IBaseReadFileOptions
	Atomic *bool `json:"atomic"` // atomic read
}

// IReadFileStreamOptions represents options for reading file streams
type IReadFileStreamOptionsStruct struct {
	IBaseReadFileOptions
}

// File system interfaces

// IFileSystemProvider represents a file system provider
type IFileSystemProvider interface {
	GetCapabilities() FileSystemProviderCapabilities
	OnDidChangeCapabilities() baseCommon.Event[struct{}]
	OnDidChangeFile() baseCommon.Event[[]IFileChange]
	OnDidWatchError() baseCommon.Event[string]

	Watch(resource *baseCommon.URI, opts IWatchOptions) baseCommon.IDisposable
	Stat(resource *baseCommon.URI) (*IStat, error)
	Mkdir(resource *baseCommon.URI) error
	Readdir(resource *baseCommon.URI) ([][2]interface{}, error) // [string, FileType]
	Delete(resource *baseCommon.URI, opts IFileDeleteOptions) error
	Rename(from, to *baseCommon.URI, opts IFileOverwriteOptions) error
	Copy(from, to *baseCommon.URI, opts IFileOverwriteOptions) error

	// Optional methods based on capabilities
	ReadFile(resource *baseCommon.URI) ([]byte, error)
	WriteFile(resource *baseCommon.URI, content []byte, opts IFileWriteOptions) error
	ReadFileStream(resource *baseCommon.URI, opts IFileReadStreamOptions, token baseCommon.CancellationToken) (baseCommon.ReadableStreamEvents[[]byte], error)
	Open(resource *baseCommon.URI, opts interface{}) (int, error) // file descriptor
	Close(fd int) error
	Read(fd int, pos int64, data []byte, offset int, length int) (int, error)
	Write(fd int, pos int64, data []byte, offset int, length int) (int, error)
	CloneFile(from, to *baseCommon.URI) error
	Realpath(resource *baseCommon.URI) (string, error)
}

// IFileChange represents a file change event
type IFileChange struct {
	Type     FileChangeType  `json:"type"`
	Resource *baseCommon.URI `json:"resource"`
	CId      *int            `json:"cId"` // correlation id
}

// IBaseFileStat represents base file stat information
type IBaseFileStat struct {
	Resource *baseCommon.URI `json:"resource"` // URI of the file/folder
	Name     string          `json:"name"`     // name (last segment of path)
	Size     *int64          `json:"size"`     // size in bytes (optional)
	Mtime    *int64          `json:"mtime"`    // modification time (optional)
	Ctime    *int64          `json:"ctime"`    // creation time (optional)
	Etag     *string         `json:"etag"`     // unique identifier (optional)
	Readonly *bool           `json:"readonly"` // readonly flag (optional)
	Locked   *bool           `json:"locked"`   // locked flag (optional)
}

// IBaseFileStatWithMetadata represents base file stat with required metadata
type IBaseFileStatWithMetadata struct {
	Resource *baseCommon.URI `json:"resource"`
	Name     string          `json:"name"`
	Size     int64           `json:"size"`
	Mtime    int64           `json:"mtime"`
	Ctime    int64           `json:"ctime"`
	Etag     string          `json:"etag"`
	Readonly bool            `json:"readonly"`
	Locked   bool            `json:"locked"`
}

// IFileStat represents file stat information with type information
type IFileStat struct {
	IBaseFileStat
	IsFile         bool        `json:"isFile"`
	IsDirectory    bool        `json:"isDirectory"`
	IsSymbolicLink bool        `json:"isSymbolicLink"`
	Children       []IFileStat `json:"children"` // nil if not a directory or not resolved
}

// IFileStatWithMetadata represents file stat with required metadata
type IFileStatWithMetadata struct {
	IBaseFileStatWithMetadata
	IsFile         bool                    `json:"isFile"`
	IsDirectory    bool                    `json:"isDirectory"`
	IsSymbolicLink bool                    `json:"isSymbolicLink"`
	Children       []IFileStatWithMetadata `json:"children"` // nil if not a directory or not resolved
}

// IFileStatWithPartialMetadata represents file stat with partial metadata
type IFileStatWithPartialMetadata struct {
	IFileStatWithMetadata
	// Children field is omitted
}

// IFileStatResult represents the result of a file stat operation
type IFileStatResult struct {
	Stat    *IFileStat `json:"stat"`
	Success bool       `json:"success"`
}

// IFileStatResultWithMetadata represents the result with metadata
type IFileStatResultWithMetadata struct {
	Stat    *IFileStatWithMetadata `json:"stat"`
	Success bool                   `json:"success"`
}

// IFileContent represents file content
type IFileContent struct {
	IBaseFileStatWithMetadata
	Value baseCommon.VSBuffer `json:"value"`
}

// IFileStreamContent represents file stream content
type IFileStreamContent struct {
	IBaseFileStatWithMetadata
	Value baseCommon.VSBufferReadableStream `json:"value"`
}

// Event interfaces

// IFileSystemProviderRegistrationEvent represents provider registration events
type IFileSystemProviderRegistrationEvent struct {
	Added    bool                `json:"added"`
	Scheme   string              `json:"scheme"`
	Provider IFileSystemProvider `json:"provider"`
}

// IFileSystemProviderCapabilitiesChangeEvent represents capability change events
type IFileSystemProviderCapabilitiesChangeEvent struct {
	Provider IFileSystemProvider `json:"provider"`
	Scheme   string              `json:"scheme"`
}

// IFileSystemProviderActivationEvent represents activation events
type IFileSystemProviderActivationEvent struct {
	Scheme string                      `json:"scheme"`
	Join   func(promise chan struct{}) `json:"-"`
}

// IFileOperationEvent represents file operation events
type IFileOperationEvent struct {
	Resource  *baseCommon.URI        `json:"resource"`
	Operation FileOperation          `json:"operation"`
	Target    *IFileStatWithMetadata `json:"target"` // optional, for CREATE/MOVE/COPY operations
}

// IsOperation checks if the event is of a specific operation type
func (e *IFileOperationEvent) IsOperation(operation FileOperation) bool {
	return e.Operation == operation
}

// FileChangesEvent represents a collection of file changes
type FileChangesEvent struct {
	changes          []IFileChange
	ignorePathCasing bool
	correlationId    *int // nil means mixed correlation
}

// NewFileChangesEvent creates a new file changes event
func NewFileChangesEvent(changes []IFileChange, ignorePathCasing bool) *FileChangesEvent {
	event := &FileChangesEvent{
		changes:          changes,
		ignorePathCasing: ignorePathCasing,
	}

	// Determine correlation ID
	if len(changes) > 0 {
		firstCId := changes[0].CId
		allSame := true
		for _, change := range changes[1:] {
			if (change.CId == nil && firstCId != nil) ||
				(change.CId != nil && firstCId == nil) ||
				(change.CId != nil && firstCId != nil && *change.CId != *firstCId) {
				allSame = false
				break
			}
		}
		if allSame {
			event.correlationId = firstCId
		}
	}

	return event
}

// GetChanges returns all changes
func (e *FileChangesEvent) GetChanges() []IFileChange {
	return e.changes
}

// Contains checks if the event contains changes for a specific resource
func (e *FileChangesEvent) Contains(resource *baseCommon.URI, types ...FileChangeType) bool {
	return e.doContains(resource, false, types...)
}

// Affects checks if the event affects a resource (including children)
func (e *FileChangesEvent) Affects(resource *baseCommon.URI, types ...FileChangeType) bool {
	return e.doContains(resource, true, types...)
}

func (e *FileChangesEvent) doContains(resource *baseCommon.URI, includeChildren bool, types ...FileChangeType) bool {
	if len(types) == 0 {
		types = []FileChangeType{FileChangeTypeUPDATED, FileChangeTypeADDED, FileChangeTypeDELETED}
	}

	for _, change := range e.changes {
		// Check if type matches
		typeMatches := false
		for _, t := range types {
			if change.Type == t {
				typeMatches = true
				break
			}
		}
		if !typeMatches {
			continue
		}

		// Check if resource matches
		if change.Resource.Equals(resource) {
			return true
		}

		if includeChildren {
			// Check if change affects a child of the resource
			// This would need proper path comparison logic
			// For now, simplified implementation
			if isParent(resource.GetPath(), change.Resource.GetPath(), e.ignorePathCasing) {
				return true
			}
		}
	}

	return false
}

// HasCorrelation checks if the event has correlation
func (e *FileChangesEvent) HasCorrelation() bool {
	return e.correlationId != nil
}

// Correlates checks if the event correlates with a specific ID
func (e *FileChangesEvent) Correlates(correlationId int) bool {
	return e.correlationId != nil && *e.correlationId == correlationId
}

// IFileSystemWatcher represents a file system watcher
type IFileSystemWatcher interface {
	baseCommon.IDisposable
	OnDidChange() baseCommon.Event[*FileChangesEvent]
}

// Error types

// FileSystemProviderError represents a file system provider error
type FileSystemProviderError struct {
	error
	Code FileSystemProviderErrorCode
}

// NewFileSystemProviderError creates a new file system provider error
func NewFileSystemProviderError(message string, code FileSystemProviderErrorCode) *FileSystemProviderError {
	return &FileSystemProviderError{
		error: errors.New(message),
		Code:  code,
	}
}

// FileOperationError represents a file operation error
type FileOperationError struct {
	error
	FileOperationResult FileOperationResult
	Options             interface{} // IReadFileOptions | IWriteFileOptions | ICreateFileOptions
}

// NewFileOperationError creates a new file operation error
func NewFileOperationError(message string, result FileOperationResult, options interface{}) *FileOperationError {
	return &FileOperationError{
		error:               errors.New(message),
		FileOperationResult: result,
		Options:             options,
	}
}

// TooLargeFileOperationError represents a file too large error
type TooLargeFileOperationError struct {
	*FileOperationError
	Size int64
}

// NewTooLargeFileOperationError creates a new too large file error
func NewTooLargeFileOperationError(message string, size int64, options interface{}) *TooLargeFileOperationError {
	return &TooLargeFileOperationError{
		FileOperationError: NewFileOperationError(message, FileOperationResultFileTooLarge, options),
		Size:               size,
	}
}

// NotModifiedSinceFileOperationError represents a not modified since error
type NotModifiedSinceFileOperationError struct {
	*FileOperationError
	Stat IFileStatWithMetadata
}

// NewNotModifiedSinceFileOperationError creates a new not modified since error
func NewNotModifiedSinceFileOperationError(message string, stat IFileStatWithMetadata, options interface{}) *NotModifiedSinceFileOperationError {
	return &NotModifiedSinceFileOperationError{
		FileOperationError: NewFileOperationError(message, FileOperationResultFileNotModifiedSince, options),
		Stat:               stat,
	}
}

// Main file service interface

// IFileServiceId is the service identifier for IFileService
var IFileServiceId = platformInstantiation.CreateDecorator[IFileService]("fileService")

// IFileService represents the main file service interface
type IFileService interface {
	// Provider management
	OnDidChangeFileSystemProviderRegistrations() baseCommon.Event[IFileSystemProviderRegistrationEvent]
	OnDidChangeFileSystemProviderCapabilities() baseCommon.Event[IFileSystemProviderCapabilitiesChangeEvent]
	OnWillActivateFileSystemProvider() baseCommon.Event[IFileSystemProviderActivationEvent]

	RegisterProvider(scheme string, provider IFileSystemProvider) baseCommon.IDisposable
	GetProvider(scheme string) IFileSystemProvider
	ActivateProvider(scheme string) error
	CanHandleResource(resource *baseCommon.URI) (bool, error)
	HasProvider(resource *baseCommon.URI) bool
	HasCapability(resource *baseCommon.URI, capability FileSystemProviderCapabilities) bool
	ListCapabilities() map[string]FileSystemProviderCapabilities

	// File change events
	OnDidFilesChange() baseCommon.Event[*FileChangesEvent]
	OnDidRunOperation() baseCommon.Event[IFileOperationEvent]
	OnDidWatchError() baseCommon.Event[error]

	// File operations
	Resolve(resource *baseCommon.URI, options *IResolveFileOptions) (*IFileStat, error)
	ResolveWithMetadata(resource *baseCommon.URI, options *IResolveMetadataFileOptions) (*IFileStatWithMetadata, error)
	ResolveAll(toResolve []struct {
		Resource *baseCommon.URI
		Options  *IResolveFileOptions
	}) ([]IFileStatResult, error)
	ResolveAllWithMetadata(toResolve []struct {
		Resource *baseCommon.URI
		Options  *IResolveMetadataFileOptions
	}) ([]IFileStatResultWithMetadata, error)
	Stat(resource *baseCommon.URI) (*IFileStatWithPartialMetadata, error)
	Realpath(resource *baseCommon.URI) (*baseCommon.URI, error)
	Exists(resource *baseCommon.URI) (bool, error)

	// File I/O
	ReadFile(resource *baseCommon.URI, options *IReadFileOptions, token baseCommon.CancellationToken) (*IFileContent, error)
	ReadFileStream(resource *baseCommon.URI, options *IReadFileStreamOptionsStruct, token baseCommon.CancellationToken) (*IFileStreamContent, error)
	WriteFile(resource *baseCommon.URI, bufferOrReadableOrStream interface{}, options *IFileWriteOptions) (*IFileStatWithMetadata, error)

	// File management
	CreateFile(resource *baseCommon.URI, bufferOrReadableOrStream interface{}, options *ICreateFileOptions) (*IFileStatWithMetadata, error)
	CreateFolder(resource *baseCommon.URI) (*IFileStatWithMetadata, error)
	Delete(resource *baseCommon.URI, options *IFileDeleteOptions) error
	Move(source, target *baseCommon.URI, overwrite bool) (*IFileStatWithMetadata, error)
	Copy(source, target *baseCommon.URI, overwrite bool) (*IFileStatWithMetadata, error)
	CloneFile(source, target *baseCommon.URI) error

	// Validation
	CanCreateFile(resource *baseCommon.URI, options *ICreateFileOptions) error
	CanDelete(resource *baseCommon.URI, options *IFileDeleteOptions) error
	CanMove(source, target *baseCommon.URI, overwrite bool) error
	CanCopy(source, target *baseCommon.URI, overwrite bool) error

	// Watching
	CreateWatcher(resource *baseCommon.URI, options IWatchOptionsWithoutCorrelation) IFileSystemWatcher
	Watch(resource *baseCommon.URI, options *IWatchOptionsWithoutCorrelation) baseCommon.IDisposable

	// Lifecycle
	Dispose()
}

// Utility functions

// isParent checks if parent is a parent of child path
func isParent(parent, child string, ignoreCase bool) bool {
	// Simplified implementation - in real code this would need proper path comparison
	if ignoreCase {
		parent = strings.ToLower(parent)
		child = strings.ToLower(child)
	}
	return strings.HasPrefix(child, parent+"/") || strings.HasPrefix(child, parent+"\\")
}

// Etag generates an etag from stat information
func Etag(stat IStat) string {
	return fmt.Sprintf("%d-%d", stat.Mtime, stat.Size)
}

// EtagOptional generates an etag from optional stat information
func EtagOptional(mtime *int64, size *int64) *string {
	if mtime == nil || size == nil {
		return nil
	}
	result := fmt.Sprintf("%d-%d", *mtime, *size)
	return &result
}

// Capability checking helper functions

// HasReadWriteCapability checks if provider has read/write capability
func HasReadWriteCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileReadWrite) != 0
}

// HasOpenReadWriteCloseCapability checks if provider has open/read/write/close capability
func HasOpenReadWriteCloseCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileOpenReadWriteClose) != 0
}

// HasFileReadStreamCapability checks if provider has file read stream capability
func HasFileReadStreamCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileReadStream) != 0
}

// HasFileFolderCopyCapability checks if provider has file/folder copy capability
func HasFileFolderCopyCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileFolderCopy) != 0
}

// HasFileAtomicReadCapability checks if provider has atomic read capability
func HasFileAtomicReadCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileAtomicRead) != 0
}

// HasFileAtomicWriteCapability checks if provider has atomic write capability
func HasFileAtomicWriteCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileAtomicWrite) != 0
}

// HasFileAtomicDeleteCapability checks if provider has atomic delete capability
func HasFileAtomicDeleteCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileAtomicDelete) != 0
}

// HasFileCloneCapability checks if provider has file clone capability
func HasFileCloneCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileClone) != 0
}

// HasFileRealpathCapability checks if provider has realpath capability
func HasFileRealpathCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesFileRealpath) != 0
}

// ETAG_DISABLED constant for disabled etag
const ETAG_DISABLED = ""

// ByteSize utility for formatting file sizes
type ByteSize struct{}

var (
	ByteSizeKB = int64(1024)
	ByteSizeMB = ByteSizeKB * ByteSizeKB
	ByteSizeGB = ByteSizeMB * ByteSizeKB
	ByteSizeTB = ByteSizeGB * ByteSizeKB
)

// FormatSize formats a size in bytes to human readable format
func (ByteSize) FormatSize(size int64) string {
	if size >= ByteSizeTB {
		return fmt.Sprintf("%.1f TB", float64(size)/float64(ByteSizeTB))
	} else if size >= ByteSizeGB {
		return fmt.Sprintf("%.1f GB", float64(size)/float64(ByteSizeGB))
	} else if size >= ByteSizeMB {
		return fmt.Sprintf("%.1f MB", float64(size)/float64(ByteSizeMB))
	} else if size >= ByteSizeKB {
		return fmt.Sprintf("%.1f KB", float64(size)/float64(ByteSizeKB))
	}
	return fmt.Sprintf("%d B", size)
}

// Error conversion utilities

// ToFileSystemProviderErrorCode converts an error to a file system provider error code
func ToFileSystemProviderErrorCode(err error) FileSystemProviderErrorCode {
	if fsErr, ok := err.(*FileSystemProviderError); ok {
		return fsErr.Code
	}
	return FileSystemProviderErrorCodeUnknown
}

// ToFileOperationResult converts an error to a file operation result
func ToFileOperationResult(err error) FileOperationResult {
	if opErr, ok := err.(*FileOperationError); ok {
		return opErr.FileOperationResult
	}

	code := ToFileSystemProviderErrorCode(err)
	switch code {
	case FileSystemProviderErrorCodeFileNotFound:
		return FileOperationResultFileNotFound
	case FileSystemProviderErrorCodeFileExists:
		return FileOperationResultFileModifiedSince
	case FileSystemProviderErrorCodeFileIsADirectory:
		return FileOperationResultFileIsDirectory
	case FileSystemProviderErrorCodeFileNotADirectory:
		return FileOperationResultFileNotDirectory
	case FileSystemProviderErrorCodeFileTooLarge:
		return FileOperationResultFileTooLarge
	case FileSystemProviderErrorCodeFileWriteLocked:
		return FileOperationResultFileWriteLocked
	case FileSystemProviderErrorCodeNoPermissions:
		return FileOperationResultFilePermissionDenied
	default:
		return FileOperationResultFileOtherError
	}
}

// EnsureFileSystemProviderError ensures an error is a proper file system provider error
func EnsureFileSystemProviderError(err error) error {
	if err == nil {
		return nil
	}

	if _, ok := err.(*FileSystemProviderError); ok {
		return err
	}

	return NewFileSystemProviderError(err.Error(), FileSystemProviderErrorCodeUnknown)
}

// CreateFileSystemProviderError creates a new file system provider error
func CreateFileSystemProviderError(message string, code FileSystemProviderErrorCode) *FileSystemProviderError {
	return NewFileSystemProviderError(message, code)
}

// MarkAsFileSystemProviderError marks an error with a specific code
func MarkAsFileSystemProviderError(err error, code FileSystemProviderErrorCode) error {
	return NewFileSystemProviderError(err.Error(), code)
}

// FileService context for dependency injection

// FileServiceContext provides context for file service dependency injection
type FileServiceContext struct {
	context.Context
	fileService IFileService
}

// NewFileServiceContext creates a new context with file service
func NewFileServiceContext(ctx context.Context, fileService IFileService) *FileServiceContext {
	return &FileServiceContext{
		Context:     ctx,
		fileService: fileService,
	}
}

// GetFileService retrieves the file service from context
func GetFileService(ctx context.Context) IFileService {
	if fsc, ok := ctx.(*FileServiceContext); ok {
		return fsc.fileService
	}
	return nil
}

// FileKind enum - MISSING
type FileKind int

const (
	FileKindFile FileKind = iota
	FileKindFolder
	FileKindRootFolder
)

// File Open Options - MISSING
type IFileOpenOptions interface{}

type IFileOpenForReadOptions struct {
	Create bool `json:"create"` // must be false
}

type IFileOpenForWriteOptions struct {
	IFileUnlockOptions
	Create bool `json:"create"` // must be true
}

// Enhanced Provider Interfaces - MISSING
type IFileSystemProviderWithFileAtomicReadCapability interface {
	IFileSystemProvider
	ReadFileAtomic(resource *baseCommon.URI, opts *IFileAtomicReadOptions) ([]byte, error)
	EnforceAtomicReadFile(resource *baseCommon.URI) bool
}

type IFileSystemProviderWithFileAtomicWriteCapability interface {
	IFileSystemProvider
	WriteFileAtomic(resource *baseCommon.URI, content []byte, opts *IFileAtomicWriteOptions) error
	EnforceAtomicWriteFile(resource *baseCommon.URI) interface{} // IFileAtomicOptions or false
}

type IFileSystemProviderWithFileAtomicDeleteCapability interface {
	IFileSystemProvider
	DeleteAtomic(resource *baseCommon.URI, opts *IFileAtomicDeleteOptions) error
	EnforceAtomicDelete(resource *baseCommon.URI) interface{} // IFileAtomicOptions or false
}

type IFileSystemProviderWithReadonlyCapability interface {
	IFileSystemProvider
	GetReadOnlyMessage() *string // equivalent to IMarkdownString
}

type IFileSystemProviderWithFileReadWriteCapability interface {
	IFileSystemProvider
	ReadFile(resource *baseCommon.URI) ([]byte, error)
	WriteFile(resource *baseCommon.URI, content []byte, opts IFileWriteOptions) error
}

type IFileSystemProviderWithFileReadStreamCapability interface {
	IFileSystemProvider
	ReadFileStream(resource *baseCommon.URI, opts IFileReadStreamOptions, token baseCommon.CancellationToken) (baseCommon.ReadableStreamEvents[[]byte], error)
}

type IFileSystemProviderWithFileFolderCopyCapability interface {
	IFileSystemProvider
	Copy(from, to *baseCommon.URI, opts IFileOverwriteOptions) error
}

type IFileSystemProviderWithFileCloneCapability interface {
	IFileSystemProvider
	CloneFile(from, to *baseCommon.URI) error
}

// Configuration Types - MISSING
type IGlobPatterns map[string]bool

type IFilesConfiguration struct {
	Files *IFilesConfigurationNode `json:"files"`
}

type IFilesConfigurationNode struct {
	Associations               map[string]string `json:"associations"`
	Exclude                    map[string]bool   `json:"exclude"`
	WatcherExclude             IGlobPatterns     `json:"watcherExclude"`
	WatcherInclude             []string          `json:"watcherInclude"`
	Encoding                   string            `json:"encoding"`
	AutoGuessEncoding          bool              `json:"autoGuessEncoding"`
	CandidateGuessEncodings    []string          `json:"candidateGuessEncodings"`
	DefaultLanguage            string            `json:"defaultLanguage"`
	TrimTrailingWhitespace     bool              `json:"trimTrailingWhitespace"`
	AutoSave                   string            `json:"autoSave"`
	AutoSaveDelay              int               `json:"autoSaveDelay"`
	AutoSaveWorkspaceFilesOnly bool              `json:"autoSaveWorkspaceFilesOnly"`
	AutoSaveWhenNoErrors       bool              `json:"autoSaveWhenNoErrors"`
	Eol                        string            `json:"eol"`
	EnableTrash                bool              `json:"enableTrash"`
	HotExit                    string            `json:"hotExit"`
	SaveConflictResolution     string            `json:"saveConflictResolution"`
	ReadonlyInclude            IGlobPatterns     `json:"readonlyInclude"`
	ReadonlyExclude            IGlobPatterns     `json:"readonlyExclude"`
	ReadonlyFromPermissions    bool              `json:"readonlyFromPermissions"`
}

// Configuration Constants - MISSING
var AutoSaveConfiguration = struct {
	OFF              string
	AFTER_DELAY      string
	ON_FOCUS_CHANGE  string
	ON_WINDOW_CHANGE string
}{
	OFF:              "off",
	AFTER_DELAY:      "afterDelay",
	ON_FOCUS_CHANGE:  "onFocusChange",
	ON_WINDOW_CHANGE: "onWindowChange",
}

var HotExitConfiguration = struct {
	OFF                      string
	ON_EXIT                  string
	ON_EXIT_AND_WINDOW_CLOSE string
}{
	OFF:                      "off",
	ON_EXIT:                  "onExit",
	ON_EXIT_AND_WINDOW_CLOSE: "onExitAndWindowClose",
}

const (
	FILES_ASSOCIATIONS_CONFIG              = "files.associations"
	FILES_EXCLUDE_CONFIG                   = "files.exclude"
	FILES_READONLY_INCLUDE_CONFIG          = "files.readonlyInclude"
	FILES_READONLY_EXCLUDE_CONFIG          = "files.readonlyExclude"
	FILES_READONLY_FROM_PERMISSIONS_CONFIG = "files.readonlyFromPermissions"
)

// Additional Utility Functions - MISSING

// IsFileOpenForWriteOptions checks if options are for write operations
func IsFileOpenForWriteOptions(options IFileOpenOptions) bool {
	if writeOpts, ok := options.(IFileOpenForWriteOptions); ok {
		return writeOpts.Create
	}
	return false
}

// WhenProviderRegistered waits for a provider to be registered for the given scheme
func WhenProviderRegistered(file *baseCommon.URI, fileService IFileService) error {
	if fileService.HasProvider(file) {
		return nil
	}

	// In Go, this would need to be implemented with channels/goroutines
	// For now, returning immediately
	return nil
}

// GetLargeFileConfirmationLimit returns the size limit for large file confirmations
func GetLargeFileConfirmationLimit(arg interface{}) int64 {
	switch v := arg.(type) {
	case string: // remote authority
		return 10 * ByteSizeMB
	case *baseCommon.URI:
		if v.GetScheme() == "file" {
			return 1024 * ByteSizeMB // Local files - almost no limit
		}
		if v.GetScheme() == "vscode-remote" {
			return 10 * ByteSizeMB // Remote files - lower limit
		}
		return 50 * ByteSizeMB // Web/other - medium limit
	default:
		return 1024 * ByteSizeMB // Default to local limit
	}
}

// IsFileSystemWatcher checks if an object is a file system watcher
func IsFileSystemWatcher(thing interface{}) bool {
	watcher, ok := thing.(IFileSystemWatcher)
	return ok && watcher != nil
}

// HasReadonlyCapability checks if provider has readonly capability
func HasReadonlyCapability(provider IFileSystemProvider) bool {
	return (provider.GetCapabilities() & FileSystemProviderCapabilitiesReadonly) != 0
}
