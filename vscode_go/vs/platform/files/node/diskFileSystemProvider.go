/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"errors"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"syscall"
	"time"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	platformLogCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// IDiskFileSystemProviderOptions represents options for disk file system provider
type IDiskFileSystemProviderOptions struct {
	Watcher *struct {
		// Extra options for the recursive file watching
		Recursive *platformFilesCommon.IRecursiveWatcherOptions `json:"recursive"`
		// Forces all file watch requests to run through a single universal file watcher
		ForceUniversal bool `json:"forceUniversal"`
	} `json:"watcher"`
}

// AbstractDiskFileSystemProvider provides base functionality for disk file system providers
type AbstractDiskFileSystemProvider struct {
	*baseCommon.Disposable
	logService platformLogCommon.ILogService
	options    *IDiskFileSystemProviderOptions

	// Events
	onDidChangeFile *baseCommon.Emitter[[]platformFilesCommon.IFileChange]
	onDidWatchError *baseCommon.Emitter[string]

	// Universal watcher
	universalWatcher             *platformFilesCommon.AbstractUniversalWatcherClient
	universalWatchRequests       []platformFilesCommon.IUniversalWatchRequest
	universalWatchRequestDelayer *baseCommon.ThrottledDelayer[interface{}]

	// Non-recursive watcher
	nonRecursiveWatcher             *platformFilesCommon.AbstractNonRecursiveWatcherClient
	nonRecursiveWatchRequests       []platformFilesCommon.INonRecursiveWatchRequest
	nonRecursiveWatchRequestDelayer *baseCommon.ThrottledDelayer[interface{}]

	mu sync.RWMutex
}

// NewAbstractDiskFileSystemProvider creates a new abstract disk file system provider
func NewAbstractDiskFileSystemProvider(
	logService platformLogCommon.ILogService,
	options *IDiskFileSystemProviderOptions,
) *AbstractDiskFileSystemProvider {
	provider := &AbstractDiskFileSystemProvider{
		Disposable:                      baseCommon.NewDisposable(),
		logService:                      logService,
		options:                         options,
		onDidChangeFile:                 baseCommon.NewEmitter[[]platformFilesCommon.IFileChange](),
		onDidWatchError:                 baseCommon.NewEmitter[string](),
		universalWatchRequestDelayer:    baseCommon.NewThrottledDelayer[interface{}](0),
		nonRecursiveWatchRequestDelayer: baseCommon.NewThrottledDelayer[interface{}](0),
	}

	// Register disposables
	provider.Register(provider.onDidChangeFile)
	provider.Register(provider.onDidWatchError)
	provider.Register(provider.universalWatchRequestDelayer)
	provider.Register(provider.nonRecursiveWatchRequestDelayer)

	return provider
}

// OnDidChangeFile returns the file change event
func (p *AbstractDiskFileSystemProvider) OnDidChangeFile() baseCommon.Event[[]platformFilesCommon.IFileChange] {
	return p.onDidChangeFile.Event()
}

// OnDidWatchError returns the watch error event
func (p *AbstractDiskFileSystemProvider) OnDidWatchError() baseCommon.Event[string] {
	return p.onDidWatchError.Event()
}

// Watch watches a resource for changes
func (p *AbstractDiskFileSystemProvider) Watch(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) baseCommon.IDisposable {
	if opts.Recursive || (p.options != nil && p.options.Watcher != nil && p.options.Watcher.ForceUniversal) {
		return p.watchUniversal(resource, opts)
	}
	return p.watchNonRecursive(resource, opts)
}

// getRefreshWatchersDelay returns the delay for refreshing watchers based on count
func (p *AbstractDiskFileSystemProvider) getRefreshWatchersDelay(count int) time.Duration {
	if count > 200 {
		// If there are many requests to refresh, start to throttle
		return 500 * time.Millisecond
	}
	// By default, use a short delay
	return 0
}

// watchUniversal watches using universal watcher
func (p *AbstractDiskFileSystemProvider) watchUniversal(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) baseCommon.IDisposable {
	p.mu.Lock()
	defer p.mu.Unlock()

	request := p.toWatchRequest(resource, opts)

	// Add to requests
	p.universalWatchRequests = append(p.universalWatchRequests, request)

	// Trigger update
	p.refreshUniversalWatchers()

	return baseCommon.ToDisposable(func() {
		p.mu.Lock()
		defer p.mu.Unlock()

		// Remove from list
		for i, req := range p.universalWatchRequests {
			if req.GetPath() == request.GetPath() {
				p.universalWatchRequests = append(p.universalWatchRequests[:i], p.universalWatchRequests[i+1:]...)
				break
			}
		}

		// Trigger update
		p.refreshUniversalWatchers()
	})
}

// stringSliceToInterfaceSlice converts a slice of strings to a slice of interfaces
func stringSliceToInterfaceSlice(input []string) []interface{} {
	if input == nil {
		return nil
	}
	output := make([]interface{}, len(input))
	for i, v := range input {
		output[i] = v
	}
	return output
}

// toWatchRequest converts to watch request
func (p *AbstractDiskFileSystemProvider) toWatchRequest(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) platformFilesCommon.IUniversalWatchRequest {
	path := p.toWatchPath(resource)

	if opts.Recursive {
		request := &platformFilesCommon.IRecursiveWatchRequest{
			IWatchRequest: platformFilesCommon.IWatchRequest{
				Path:          path,
				Recursive:     opts.Recursive,
				Excludes:      opts.Excludes,
				Includes:      stringSliceToInterfaceSlice(opts.Includes),
				CorrelationId: opts.CorrelationId,
				Filter:        opts.Filter,
			},
		}

		// Adjust for polling
		if p.options != nil && p.options.Watcher != nil && p.options.Watcher.Recursive != nil {
			usePolling := p.options.Watcher.Recursive.UsePolling
			if usePolling != nil {
				if boolVal, ok := usePolling.(bool); ok && boolVal {
					pollingInterval := 5000
					if p.options.Watcher.Recursive.PollingInterval != nil {
						pollingInterval = *p.options.Watcher.Recursive.PollingInterval
					}
					request.PollingInterval = &pollingInterval
				} else if arrayVal, ok := usePolling.([]string); ok {
					for _, pathPattern := range arrayVal {
						if pathPattern == path {
							pollingInterval := 5000
							if p.options.Watcher.Recursive.PollingInterval != nil {
								pollingInterval = *p.options.Watcher.Recursive.PollingInterval
							}
							request.PollingInterval = &pollingInterval
							break
						}
					}
				}
			}
		}

		return request
	}

	return &platformFilesCommon.INonRecursiveWatchRequest{
		IWatchRequest: platformFilesCommon.IWatchRequest{
			Path:          path,
			Recursive:     false,
			Excludes:      opts.Excludes,
			Includes:      stringSliceToInterfaceSlice(opts.Includes),
			CorrelationId: opts.CorrelationId,
			Filter:        opts.Filter,
		},
	}
}

// refreshUniversalWatchers refreshes universal watchers
func (p *AbstractDiskFileSystemProvider) refreshUniversalWatchers() {
	delay := p.getRefreshWatchersDelay(len(p.universalWatchRequests))
	p.universalWatchRequestDelayer.Trigger(func() <-chan interface{} {
		result := make(chan interface{}, 1)
		go func() {
			defer close(result)
			p.doRefreshUniversalWatchers()
			result <- nil
		}()
		return result
	}, delay)
}

// doRefreshUniversalWatchers performs the actual refresh of universal watchers
func (p *AbstractDiskFileSystemProvider) doRefreshUniversalWatchers() {
	// Create watcher if this is the first time
	if p.universalWatcher == nil {
		p.universalWatcher = p.createUniversalWatcher(
			func(changes []platformFilesCommon.IFileChange) {
				p.onDidChangeFile.Fire(platformFilesCommon.ReviveFileChanges(changes))
			},
			func(msg platformFilesCommon.ILogMessage) {
				p.onWatcherLogMessage(msg)
			},
			p.logService.GetLevel() == platformLogCommon.LogLevelTrace,
		)

		// Register for disposal
		p.Register(p.universalWatcher)

		// Note: Go version doesn't have dynamic log level changes yet
		// TODO: Implement log level change events when available
	}

	// Ask to watch the provided paths
	if p.universalWatcher != nil {
		p.universalWatcher.Watch(p.universalWatchRequests)
	}
}

// createUniversalWatcher creates a universal watcher (abstract method)
func (p *AbstractDiskFileSystemProvider) createUniversalWatcher(
	onChange func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *platformFilesCommon.AbstractUniversalWatcherClient {
	panic("createUniversalWatcher must be implemented by subclass")
}

// watchNonRecursive watches using non-recursive watcher
func (p *AbstractDiskFileSystemProvider) watchNonRecursive(resource *baseCommon.URI, opts platformFilesCommon.IWatchOptions) baseCommon.IDisposable {
	p.mu.Lock()
	defer p.mu.Unlock()

	request := &platformFilesCommon.INonRecursiveWatchRequest{
		IWatchRequest: platformFilesCommon.IWatchRequest{
			Path:          p.toWatchPath(resource),
			Recursive:     false,
			Excludes:      opts.Excludes,
			Includes:      stringSliceToInterfaceSlice(opts.Includes),
			CorrelationId: opts.CorrelationId,
			Filter:        opts.Filter,
		},
	}

	// Add to requests
	p.nonRecursiveWatchRequests = append(p.nonRecursiveWatchRequests, *request)

	// Trigger update
	p.refreshNonRecursiveWatchers()

	return baseCommon.ToDisposable(func() {
		p.mu.Lock()
		defer p.mu.Unlock()

		// Remove from list
		for i, req := range p.nonRecursiveWatchRequests {
			if req.Path == request.Path {
				p.nonRecursiveWatchRequests = append(p.nonRecursiveWatchRequests[:i], p.nonRecursiveWatchRequests[i+1:]...)
				break
			}
		}

		// Trigger update
		p.refreshNonRecursiveWatchers()
	})
}

// refreshNonRecursiveWatchers refreshes non-recursive watchers
func (p *AbstractDiskFileSystemProvider) refreshNonRecursiveWatchers() {
	delay := p.getRefreshWatchersDelay(len(p.nonRecursiveWatchRequests))
	p.nonRecursiveWatchRequestDelayer.Trigger(func() <-chan interface{} {
		result := make(chan interface{}, 1)
		go func() {
			defer close(result)
			p.doRefreshNonRecursiveWatchers()
			result <- nil
		}()
		return result
	}, delay)
}

// doRefreshNonRecursiveWatchers performs the actual refresh of non-recursive watchers
func (p *AbstractDiskFileSystemProvider) doRefreshNonRecursiveWatchers() {
	// Create watcher if this is the first time
	if p.nonRecursiveWatcher == nil {
		p.nonRecursiveWatcher = p.createNonRecursiveWatcher(
			func(changes []platformFilesCommon.IFileChange) {
				p.onDidChangeFile.Fire(platformFilesCommon.ReviveFileChanges(changes))
			},
			func(msg platformFilesCommon.ILogMessage) {
				p.onWatcherLogMessage(msg)
			},
			p.logService.GetLevel() == platformLogCommon.LogLevelTrace,
		)

		// Register for disposal
		p.Register(p.nonRecursiveWatcher)

		// Note: Go version doesn't have dynamic log level changes yet
		// TODO: Implement log level change events when available
	}

	// Ask to watch the provided paths
	if p.nonRecursiveWatcher != nil {
		p.nonRecursiveWatcher.Watch(p.nonRecursiveWatchRequests)
	}
}

// createNonRecursiveWatcher creates a non-recursive watcher (abstract method)
func (p *AbstractDiskFileSystemProvider) createNonRecursiveWatcher(
	onChange func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *platformFilesCommon.AbstractNonRecursiveWatcherClient {
	panic("createNonRecursiveWatcher must be implemented by subclass")
}

// onWatcherLogMessage handles watcher log messages
func (p *AbstractDiskFileSystemProvider) onWatcherLogMessage(msg platformFilesCommon.ILogMessage) {
	if msg.Type == "error" {
		p.onDidWatchError.Fire(msg.Message)
	}
	p.logWatcherMessage(msg)
}

// logWatcherMessage logs watcher messages
func (p *AbstractDiskFileSystemProvider) logWatcherMessage(msg platformFilesCommon.ILogMessage) {
	switch msg.Type {
	case "trace":
		p.logService.Trace(msg.Message)
	case "debug":
		p.logService.Debug(msg.Message)
	case "info":
		p.logService.Info(msg.Message)
	case "warn":
		p.logService.Warn(msg.Message)
	case "error":
		p.logService.Error(msg.Message)
	}
}

// ToFilePath converts URI to file path
func (p *AbstractDiskFileSystemProvider) ToFilePath(resource *baseCommon.URI) string {
	return baseCommon.Normalize(resource.FSPath())
}

// toWatchPath converts URI to watch path
func (p *AbstractDiskFileSystemProvider) toWatchPath(resource *baseCommon.URI) string {
	filePath := p.ToFilePath(resource)
	// Ensure to have any trailing path separators removed
	return baseCommon.RemoveTrailingPathSeparator(filePath)
}

// Barrier provides a synchronization barrier for resource locking
type Barrier struct {
	promise *baseCommon.DeferredPromise[interface{}]
	mu      sync.RWMutex
	opened  bool
}

// NewBarrier creates a new barrier
func NewBarrier() *Barrier {
	return &Barrier{
		promise: baseCommon.NewDeferredPromise[interface{}](),
	}
}

// Wait waits for the barrier to be opened
func (b *Barrier) Wait() <-chan interface{} {
	b.mu.RLock()
	defer b.mu.RUnlock()

	if b.opened {
		// Return a channel that's already ready
		result := make(chan interface{}, 1)
		result <- nil
		close(result)
		return result
	}

	return b.promise.Promise()
}

// Open opens the barrier, allowing all waiting goroutines to proceed
func (b *Barrier) Open() {
	b.mu.Lock()
	defer b.mu.Unlock()

	if !b.opened {
		b.opened = true
		b.promise.Complete(nil)
	}
}

// DiskFileSystemProvider implements a complete file system provider for local disk access
type DiskFileSystemProvider struct {
	*AbstractDiskFileSystemProvider

	// Environment service for context
	environmentService environmentCommon.INativeEnvironmentService

	// File locking and validation
	fileLockMap   map[string]*sync.RWMutex
	fileLockMutex sync.RWMutex

	// Resource locking for atomic operations
	resourceLocks *baseCommon.ResourceMap[*Barrier]

	// File handle management
	mapHandleToPos  map[int]int64
	mapHandleToLock map[int]baseCommon.IDisposable
	writeHandles    map[int]*baseCommon.URI
	handleMutex     sync.RWMutex
	nextHandle      int

	// Capabilities
	capabilities platformFilesCommon.FileSystemProviderCapabilities

	// Events
	onDidChangeCapabilities *baseCommon.Emitter[struct{}]

	// Trash/recycle bin support
	recycleSupport bool

	// Tracing
	traceResourceLocks bool
}

// NewDiskFileSystemProvider creates a new disk file system provider
func NewDiskFileSystemProvider(
	logService platformLogCommon.ILogService,
	environmentService environmentCommon.INativeEnvironmentService,
	options *IDiskFileSystemProviderOptions,
) *DiskFileSystemProvider {
	provider := &DiskFileSystemProvider{
		AbstractDiskFileSystemProvider: NewAbstractDiskFileSystemProvider(logService, options),
		environmentService:             environmentService,
		fileLockMap:                    make(map[string]*sync.RWMutex),
		onDidChangeCapabilities:        baseCommon.NewEmitter[struct{}](),
		recycleSupport:                 true, // Default to true, can be disabled
		resourceLocks:                  baseCommon.NewResourceMap[*Barrier](),
		mapHandleToPos:                 make(map[int]int64),
		mapHandleToLock:                make(map[int]baseCommon.IDisposable),
		writeHandles:                   make(map[int]*baseCommon.URI),
		handleMutex:                    sync.RWMutex{},
		nextHandle:                     0,
	}

	// Initialize capabilities
	provider.capabilities = platformFilesCommon.FileSystemProviderCapabilitiesFileReadWrite |
		platformFilesCommon.FileSystemProviderCapabilitiesFileOpenReadWriteClose |
		platformFilesCommon.FileSystemProviderCapabilitiesFileReadStream |
		platformFilesCommon.FileSystemProviderCapabilitiesFileFolderCopy |
		platformFilesCommon.FileSystemProviderCapabilitiesPathCaseSensitive |
		platformFilesCommon.FileSystemProviderCapabilitiesReadonly

	// Add atomic capabilities if supported
	if provider.supportsAtomic() {
		provider.capabilities |= platformFilesCommon.FileSystemProviderCapabilitiesFileAtomicRead |
			platformFilesCommon.FileSystemProviderCapabilitiesFileAtomicWrite |
			platformFilesCommon.FileSystemProviderCapabilitiesFileAtomicDelete
	}

	// Add trash capability if supported
	if provider.recycleSupport {
		provider.capabilities |= platformFilesCommon.FileSystemProviderCapabilitiesTrash
	}

	// Register disposal
	provider.Register(provider.onDidChangeCapabilities)

	return provider
}

// GetCapabilities returns the provider capabilities
func (p *DiskFileSystemProvider) GetCapabilities() platformFilesCommon.FileSystemProviderCapabilities {
	return p.capabilities
}

// OnDidChangeCapabilities returns the capability change event
func (p *DiskFileSystemProvider) OnDidChangeCapabilities() baseCommon.Event[struct{}] {
	return p.onDidChangeCapabilities.Event()
}

// supportsAtomic checks if the file system supports atomic operations
func (p *DiskFileSystemProvider) supportsAtomic() bool {
	// On Windows, atomic operations are supported via MoveFileEx
	// On POSIX systems, atomic operations are supported via rename
	return true
}

// Stat returns file/directory information
func (p *DiskFileSystemProvider) Stat(resource *baseCommon.URI) (*platformFilesCommon.IStat, error) {
	filePath := p.ToFilePath(resource)

	fileInfo, err := os.Lstat(filePath) // Use Lstat to detect symlinks
	if err != nil {
		if os.IsNotExist(err) {
			return nil, platformFilesCommon.NewFileSystemProviderError(
				"File not found: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
			)
		}
		return nil, platformFilesCommon.NewFileSystemProviderError(
			"Failed to stat file: "+err.Error(),
			platformFilesCommon.FileSystemProviderErrorCodeUnknown,
		)
	}

	// Determine file type
	var fileType platformFilesCommon.FileType
	if fileInfo.Mode()&os.ModeSymlink != 0 {
		fileType = platformFilesCommon.FileTypeSymbolicLink
	} else if fileInfo.IsDir() {
		fileType = platformFilesCommon.FileTypeDirectory
	} else {
		fileType = platformFilesCommon.FileTypeFile
	}

	// Get permissions
	permissions := p.getFilePermissions(fileInfo)

	return &platformFilesCommon.IStat{
		Type:        fileType,
		Mtime:       fileInfo.ModTime().UnixMilli(),
		Ctime:       p.getCreationTime(fileInfo),
		Size:        fileInfo.Size(),
		Permissions: permissions,
	}, nil
}

// getFilePermissions extracts file permissions
func (p *DiskFileSystemProvider) getFilePermissions(fileInfo os.FileInfo) *platformFilesCommon.FilePermission {
	var permissions platformFilesCommon.FilePermission

	// Check readonly permission
	if fileInfo.Mode().Perm()&0200 == 0 { // Write permission bit not set
		permissions |= platformFilesCommon.FilePermissionReadonly
	}

	// Platform-specific locked file detection
	if p.isFileLocked(fileInfo) {
		permissions |= platformFilesCommon.FilePermissionLocked
	}

	return &permissions
}

// isFileLocked checks if a file is locked (platform-specific)
func (p *DiskFileSystemProvider) isFileLocked(fileInfo os.FileInfo) bool {
	// Simplified implementation - in reality this would use platform-specific APIs
	// On Windows: check for FILE_ATTRIBUTE_READONLY or sharing violations
	// On Unix: check for file locks via fcntl
	return false
}

// getCreationTime gets creation time (platform-specific)
func (p *DiskFileSystemProvider) getCreationTime(fileInfo os.FileInfo) int64 {
	// Default to modification time
	ctime := fileInfo.ModTime().UnixMilli()

	// Platform-specific creation time extraction
	if sys := fileInfo.Sys(); sys != nil {
		if stat, ok := sys.(*syscall.Stat_t); ok {
			switch runtime.GOOS {
			case "linux":
				// Linux doesn't have creation time, use ctime (metadata change time)
				// Note: Some Linux systems might not have nanosecond precision
				ctime = int64(stat.Ctimespec.Sec)*1000 + int64(stat.Ctimespec.Nsec)/1000000
			case "darwin":
				// macOS has creation time in the birth time field
				if stat.Birthtimespec.Sec != 0 {
					ctime = stat.Birthtimespec.Sec*1000 + stat.Birthtimespec.Nsec/1000000
				}
			case "windows":
				// Windows creation time would be extracted differently
				// For now, use modification time
			}
		}
	}

	return ctime
}

// Mkdir creates a directory
func (p *DiskFileSystemProvider) Mkdir(resource *baseCommon.URI) error {
	filePath := p.ToFilePath(resource)

	err := os.MkdirAll(filePath, 0755)
	if err != nil {
		if os.IsExist(err) {
			return platformFilesCommon.NewFileSystemProviderError(
				"Directory already exists: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileExists,
			)
		}
		return platformFilesCommon.NewFileSystemProviderError(
			"Failed to create directory: "+err.Error(),
			platformFilesCommon.FileSystemProviderErrorCodeUnknown,
		)
	}

	return nil
}

// Readdir reads directory contents
func (p *DiskFileSystemProvider) Readdir(resource *baseCommon.URI) ([][2]interface{}, error) {
	filePath := p.ToFilePath(resource)

	entries, err := os.ReadDir(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, platformFilesCommon.NewFileSystemProviderError(
				"Directory not found: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
			)
		}
		return nil, platformFilesCommon.NewFileSystemProviderError(
			"Failed to read directory: "+err.Error(),
			platformFilesCommon.FileSystemProviderErrorCodeUnknown,
		)
	}

	result := make([][2]interface{}, len(entries))
	for i, entry := range entries {
		var fileType platformFilesCommon.FileType
		if entry.IsDir() {
			fileType = platformFilesCommon.FileTypeDirectory
		} else {
			fileType = platformFilesCommon.FileTypeFile
		}

		result[i] = [2]interface{}{entry.Name(), fileType}
	}

	return result, nil
}

// Delete removes a file or directory
func (p *DiskFileSystemProvider) Delete(resource *baseCommon.URI, opts platformFilesCommon.IFileDeleteOptions) error {
	filePath := p.ToFilePath(resource)

	// Check if using trash
	if opts.UseTrash && p.recycleSupport {
		return p.moveToTrash(filePath)
	}

	// Check if atomic delete is requested
	if opts.Atomic != nil && opts.Atomic != false {
		return p.deleteAtomic(filePath, opts)
	}

	// Regular delete
	var err error
	if opts.Recursive {
		err = os.RemoveAll(filePath)
	} else {
		err = os.Remove(filePath)
	}

	if err != nil {
		if os.IsNotExist(err) {
			return platformFilesCommon.NewFileSystemProviderError(
				"File not found: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
			)
		}
		return platformFilesCommon.NewFileSystemProviderError(
			"Failed to delete: "+err.Error(),
			platformFilesCommon.FileSystemProviderErrorCodeUnknown,
		)
	}

	return nil
}

// moveToTrash moves a file to the system trash/recycle bin
func (p *DiskFileSystemProvider) moveToTrash(filePath string) error {
	// Platform-specific trash implementation
	switch runtime.GOOS {
	case "windows":
		// On Windows, would use SHFileOperation or IFileOperation
		return p.moveToRecycleBin(filePath)
	case "darwin":
		// On macOS, would move to ~/.Trash
		return p.moveToMacTrash(filePath)
	case "linux":
		// On Linux, would use freedesktop.org trash specification
		return p.moveToLinuxTrash(filePath)
	default:
		// Fallback to regular delete
		return os.RemoveAll(filePath)
	}
}

// moveToRecycleBin moves file to Windows recycle bin (stub)
func (p *DiskFileSystemProvider) moveToRecycleBin(filePath string) error {
	// Would implement Windows-specific recycle bin API
	return errors.New("recycle bin not implemented")
}

// moveToMacTrash moves file to macOS trash (stub)
func (p *DiskFileSystemProvider) moveToMacTrash(filePath string) error {
	// Would move to ~/.Trash directory
	return errors.New("macOS trash not implemented")
}

// moveToLinuxTrash moves file to Linux trash (stub)
func (p *DiskFileSystemProvider) moveToLinuxTrash(filePath string) error {
	// Would implement freedesktop.org trash specification
	return errors.New("Linux trash not implemented")
}

// deleteAtomic performs atomic delete operation
func (p *DiskFileSystemProvider) deleteAtomic(filePath string, opts platformFilesCommon.IFileDeleteOptions) error {
	// Atomic delete: first rename to temporary name, then delete
	atomicOpts, ok := opts.Atomic.(platformFilesCommon.IFileAtomicOptions)
	if !ok {
		atomicOpts = platformFilesCommon.IFileAtomicOptions{Postfix: ".vsctmp"}
	}

	tempPath := filePath + atomicOpts.Postfix

	// First rename
	if err := os.Rename(filePath, tempPath); err != nil {
		return err
	}

	// Then delete
	var err error
	if opts.Recursive {
		err = os.RemoveAll(tempPath)
	} else {
		err = os.Remove(tempPath)
	}

	return err
}

// Rename moves/renames a file or directory
func (p *DiskFileSystemProvider) Rename(from, to *baseCommon.URI, opts platformFilesCommon.IFileOverwriteOptions) error {
	fromPath := p.ToFilePath(from)
	toPath := p.ToFilePath(to)

	// Check if target exists
	if !opts.Overwrite {
		if _, err := os.Stat(toPath); err == nil {
			return platformFilesCommon.NewFileSystemProviderError(
				"Target already exists: "+toPath,
				platformFilesCommon.FileSystemProviderErrorCodeFileExists,
			)
		}
	}

	// Ensure target directory exists
	if err := os.MkdirAll(filepath.Dir(toPath), 0755); err != nil {
		return err
	}

	// Perform rename
	if err := os.Rename(fromPath, toPath); err != nil {
		return platformFilesCommon.NewFileSystemProviderError(
			"Failed to rename: "+err.Error(),
			platformFilesCommon.FileSystemProviderErrorCodeUnknown,
		)
	}

	return nil
}

// Copy copies a file or directory
func (p *DiskFileSystemProvider) Copy(from, to *baseCommon.URI, opts platformFilesCommon.IFileOverwriteOptions) error {
	fromPath := p.ToFilePath(from)
	toPath := p.ToFilePath(to)

	// Check if target exists
	if !opts.Overwrite {
		if _, err := os.Stat(toPath); err == nil {
			return platformFilesCommon.NewFileSystemProviderError(
				"Target already exists: "+toPath,
				platformFilesCommon.FileSystemProviderErrorCodeFileExists,
			)
		}
	}

	// Get source info
	sourceInfo, err := os.Lstat(fromPath)
	if err != nil {
		return err
	}

	// Ensure target directory exists
	if err := os.MkdirAll(filepath.Dir(toPath), 0755); err != nil {
		return err
	}

	if sourceInfo.IsDir() {
		return p.copyDirectory(fromPath, toPath)
	} else {
		return p.copyFile(fromPath, toPath)
	}
}

// copyFile copies a single file
func (p *DiskFileSystemProvider) copyFile(from, to string) error {
	sourceFile, err := os.Open(from)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(to)
	if err != nil {
		return err
	}
	defer destFile.Close()

	// Copy file contents
	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	// Copy file permissions
	sourceInfo, err := os.Stat(from)
	if err != nil {
		return err
	}

	return os.Chmod(to, sourceInfo.Mode())
}

// copyDirectory recursively copies a directory
func (p *DiskFileSystemProvider) copyDirectory(from, to string) error {
	// Create destination directory
	sourceInfo, err := os.Stat(from)
	if err != nil {
		return err
	}

	if err := os.MkdirAll(to, sourceInfo.Mode()); err != nil {
		return err
	}

	// Read source directory
	entries, err := os.ReadDir(from)
	if err != nil {
		return err
	}

	// Copy each entry
	for _, entry := range entries {
		sourcePath := filepath.Join(from, entry.Name())
		destPath := filepath.Join(to, entry.Name())

		if entry.IsDir() {
			if err := p.copyDirectory(sourcePath, destPath); err != nil {
				return err
			}
		} else {
			if err := p.copyFile(sourcePath, destPath); err != nil {
				return err
			}
		}
	}

	return nil
}

// ReadFile reads the entire contents of a file
func (p *DiskFileSystemProvider) ReadFile(resource *baseCommon.URI) ([]byte, error) {
	filePath := p.ToFilePath(resource)

	data, err := os.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, platformFilesCommon.NewFileSystemProviderError(
				"File not found: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
			)
		}
		return nil, platformFilesCommon.NewFileSystemProviderError(
			"Failed to read file: "+err.Error(),
			platformFilesCommon.FileSystemProviderErrorCodeUnknown,
		)
	}

	return data, nil
}

// WriteFile writes data to a file
func (p *DiskFileSystemProvider) WriteFile(resource *baseCommon.URI, content []byte, opts platformFilesCommon.IFileWriteOptions) error {
	filePath := p.ToFilePath(resource)

	// Check if file exists and handle creation/overwrite
	_, statErr := os.Stat(filePath)
	fileExists := statErr == nil

	if !fileExists && !opts.Create {
		return platformFilesCommon.NewFileSystemProviderError(
			"File does not exist and create flag is false: "+filePath,
			platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
		)
	}

	if fileExists && !opts.Overwrite {
		return platformFilesCommon.NewFileSystemProviderError(
			"File exists and overwrite flag is false: "+filePath,
			platformFilesCommon.FileSystemProviderErrorCodeFileExists,
		)
	}

	// Handle unlock option
	if opts.Unlock && fileExists {
		if err := p.unlockFile(filePath); err != nil {
			return err
		}
	}

	// Handle atomic write
	if opts.Atomic != nil && opts.Atomic != false {
		return p.writeFileAtomic(filePath, content, opts)
	}

	// Ensure directory exists
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return err
	}

	// Regular write
	if err := os.WriteFile(filePath, content, 0644); err != nil {
		return platformFilesCommon.NewFileSystemProviderError(
			"Failed to write file: "+err.Error(),
			platformFilesCommon.FileSystemProviderErrorCodeUnknown,
		)
	}

	// Set modification time if specified
	if opts.Mtime != nil {
		modTime := time.UnixMilli(*opts.Mtime)
		if err := os.Chtimes(filePath, modTime, modTime); err != nil {
			// Non-fatal error
			p.logService.Warn("Failed to set modification time: " + err.Error())
		}
	}

	return nil
}

// unlockFile removes readonly/locked attributes from a file
func (p *DiskFileSystemProvider) unlockFile(filePath string) error {
	// Get current file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return err
	}

	// Make file writable
	newMode := fileInfo.Mode() | 0200 // Add write permission
	return os.Chmod(filePath, newMode)
}

// writeFileAtomic performs atomic file write
func (p *DiskFileSystemProvider) writeFileAtomic(filePath string, content []byte, opts platformFilesCommon.IFileWriteOptions) error {
	atomicOpts, ok := opts.Atomic.(platformFilesCommon.IFileAtomicOptions)
	if !ok {
		atomicOpts = platformFilesCommon.IFileAtomicOptions{Postfix: ".vsctmp"}
	}

	tempPath := filePath + atomicOpts.Postfix

	// Write to temporary file
	if err := os.WriteFile(tempPath, content, 0644); err != nil {
		return err
	}

	// Atomic rename
	if err := os.Rename(tempPath, filePath); err != nil {
		os.Remove(tempPath) // Clean up temp file
		return err
	}

	// Set modification time if specified
	if opts.Mtime != nil {
		modTime := time.UnixMilli(*opts.Mtime)
		if err := os.Chtimes(filePath, modTime, modTime); err != nil {
			p.logService.Warn("Failed to set modification time: " + err.Error())
		}
	}

	return nil
}

// ReadFileStream reads a file as a stream
func (p *DiskFileSystemProvider) ReadFileStream(resource *baseCommon.URI, opts platformFilesCommon.IFileReadStreamOptions, token baseCommon.CancellationToken) (baseCommon.ReadableStreamEvents[[]byte], error) {
	filePath := p.ToFilePath(resource)

	file, err := os.Open(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, platformFilesCommon.NewFileSystemProviderError(
				"File not found: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
			)
		}
		return nil, err
	}

	// Seek to position if specified
	if opts.Position != nil {
		if _, err := file.Seek(*opts.Position, io.SeekStart); err != nil {
			file.Close()
			return nil, err
		}
	}

	// Create readable stream from file similar to TypeScript implementation
	return p.createFileReadStream(file, 64*1024, opts.Length), nil
}

// createFileReadStream creates a readable stream from a file
func (p *DiskFileSystemProvider) createFileReadStream(file *os.File, bufferSize int, length *int64) baseCommon.ReadableStreamEvents[[]byte] {
	stream := &FileReadStream{
		file:       file,
		bufferSize: bufferSize,
		length:     length,
		position:   0,
		ended:      false,
		listeners:  make(map[string][]func(interface{})),
	}

	// Start reading in a goroutine
	go stream.startReading()

	return stream
}

// FileReadStream implements ReadableStreamEvents for file reading
type FileReadStream struct {
	file       *os.File
	bufferSize int
	length     *int64
	position   int64
	ended      bool
	listeners  map[string][]func(interface{})
	mu         sync.RWMutex
}

func (s *FileReadStream) OnData(callback func([]byte)) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.listeners["data"] == nil {
		s.listeners["data"] = make([]func(interface{}), 0)
	}
	s.listeners["data"] = append(s.listeners["data"], func(data interface{}) {
		if bytes, ok := data.([]byte); ok {
			callback(bytes)
		}
	})
}

func (s *FileReadStream) OnError(callback func(error)) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.listeners["error"] == nil {
		s.listeners["error"] = make([]func(interface{}), 0)
	}
	s.listeners["error"] = append(s.listeners["error"], func(data interface{}) {
		if err, ok := data.(error); ok {
			callback(err)
		}
	})
}

func (s *FileReadStream) OnEnd(callback func()) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.listeners["end"] == nil {
		s.listeners["end"] = make([]func(interface{}), 0)
	}
	s.listeners["end"] = append(s.listeners["end"], func(data interface{}) {
		callback()
	})
}

func (s *FileReadStream) emit(event string, data interface{}) {
	s.mu.RLock()
	listeners := s.listeners[event]
	s.mu.RUnlock()

	for _, listener := range listeners {
		listener(data)
	}
}

func (s *FileReadStream) startReading() {
	defer s.file.Close()

	buffer := make([]byte, s.bufferSize)

	for {
		if s.length != nil && s.position >= *s.length {
			break
		}

		n, err := s.file.Read(buffer)
		if err != nil {
			if err == io.EOF {
				break
			}
			s.emit("error", err)
			return
		}

		if n > 0 {
			// If we have a length limit, don't read past it
			if s.length != nil && s.position+int64(n) > *s.length {
				n = int(*s.length - s.position)
			}

			data := make([]byte, n)
			copy(data, buffer[:n])
			s.emit("data", data)
			s.position += int64(n)
		}
	}

	s.mu.Lock()
	s.ended = true
	s.mu.Unlock()

	s.emit("end", nil)
}

// Open opens a file and returns a file descriptor
func (p *DiskFileSystemProvider) Open(resource *baseCommon.URI, opts interface{}) (int, error) {
	filePath := p.ToFilePath(resource)

	var flags int
	var mode os.FileMode = 0644

	// Determine flags based on options
	if openOpts, ok := opts.(platformFilesCommon.IFileOpenForReadOptions); ok {
		if openOpts.Create {
			flags = os.O_RDWR | os.O_CREATE
		} else {
			flags = os.O_RDONLY
		}
	} else if writeOpts, ok := opts.(platformFilesCommon.IFileOpenForWriteOptions); ok {
		flags = os.O_RDWR
		if writeOpts.Create {
			flags |= os.O_CREATE
		}
		if writeOpts.Unlock {
			// Handle unlock
			p.unlockFile(filePath)
		}
	} else {
		flags = os.O_RDONLY
	}

	file, err := os.OpenFile(filePath, flags, mode)
	if err != nil {
		if os.IsNotExist(err) {
			return -1, platformFilesCommon.NewFileSystemProviderError(
				"File not found: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
			)
		}
		return -1, err
	}

	// Return file descriptor
	// In a real implementation, we'd track open files in a map
	return int(file.Fd()), nil
}

// Close closes a file descriptor
func (p *DiskFileSystemProvider) Close(fd int) error {
	// In a real implementation, we'd lookup the file from fd and close it
	// For now, this is a stub
	return nil
}

// Read reads data from an open file descriptor
func (p *DiskFileSystemProvider) Read(fd int, pos int64, data []byte, offset int, length int) (int, error) {
	// In a real implementation, we'd lookup the file from fd and read from it
	// For now, this is a stub
	return 0, errors.New("read from fd not implemented")
}

// Write writes data to an open file descriptor
func (p *DiskFileSystemProvider) Write(fd int, pos int64, data []byte, offset int, length int) (int, error) {
	// In a real implementation, we'd lookup the file from fd and write to it
	// For now, this is a stub
	return 0, errors.New("write to fd not implemented")
}

// CloneFile creates a copy-on-write clone of a file (if supported by filesystem)
func (p *DiskFileSystemProvider) CloneFile(from, to *baseCommon.URI) error {
	// Most filesystems don't support CoW cloning, so fallback to copy
	return p.Copy(from, to, platformFilesCommon.IFileOverwriteOptions{Overwrite: true})
}

// Realpath resolves symbolic links and returns the canonical path
func (p *DiskFileSystemProvider) Realpath(resource *baseCommon.URI) (string, error) {
	filePath := p.ToFilePath(resource)

	realPath, err := filepath.EvalSymlinks(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return "", platformFilesCommon.NewFileSystemProviderError(
				"File not found: "+filePath,
				platformFilesCommon.FileSystemProviderErrorCodeFileNotFound,
			)
		}
		return "", err
	}

	return realPath, nil
}

// createUniversalWatcher creates a universal watcher implementation
func (p *DiskFileSystemProvider) createUniversalWatcher(
	onChange func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *platformFilesCommon.AbstractUniversalWatcherClient {
	// For now, return a stub implementation
	// In a complete implementation, this would return a properly implemented UniversalWatcherClient
	return platformFilesCommon.NewAbstractUniversalWatcherClient(onChange, onLogMessage, verboseLogging)
}

// createNonRecursiveWatcher creates a non-recursive watcher implementation
func (p *DiskFileSystemProvider) createNonRecursiveWatcher(
	onChange func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *platformFilesCommon.AbstractNonRecursiveWatcherClient {
	// For now, return a stub implementation
	return platformFilesCommon.NewAbstractNonRecursiveWatcherClient(onChange, onLogMessage, verboseLogging)
}

// getFileLock gets or creates a file lock for a path
func (p *DiskFileSystemProvider) getFileLock(filePath string) *sync.RWMutex {
	p.fileLockMutex.Lock()
	defer p.fileLockMutex.Unlock()

	if lock, exists := p.fileLockMap[filePath]; exists {
		return lock
	}

	lock := &sync.RWMutex{}
	p.fileLockMap[filePath] = lock
	return lock
}

// Dispose cleans up the provider
func (p *DiskFileSystemProvider) Dispose() {
	p.AbstractDiskFileSystemProvider.Dispose()

	p.fileLockMutex.Lock()
	p.fileLockMap = make(map[string]*sync.RWMutex)
	p.fileLockMutex.Unlock()
}

// createResourceLock creates a resource lock for atomic operations
func (p *DiskFileSystemProvider) createResourceLock(resource *baseCommon.URI) (baseCommon.IDisposable, error) {
	filePath := p.ToFilePath(resource)
	if p.traceResourceLocks {
		p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - request to acquire resource lock (" + filePath + ")")
	}

	// Await pending locks for resource. It is possible for a new lock being
	// added right after opening, so we have to loop over locks until no lock
	// remains.
	for {
		existingLock := p.resourceLocks.Get(*resource)
		if existingLock == nil {
			break
		}

		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - waiting for resource lock to be released (" + filePath + ")")
		}

		<-existingLock.Wait()
	}

	// Store new lock
	newLock := NewBarrier()
	p.resourceLocks.Set(*resource, newLock)

	if p.traceResourceLocks {
		p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - new resource lock created (" + filePath + ")")
	}

	return baseCommon.ToDisposable(func() {
		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - resource lock dispose() (" + filePath + ")")
		}

		// Delete lock if it is still ours
		if p.resourceLocks.Get(*resource) == newLock {
			if p.traceResourceLocks {
				p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - resource lock removed from resource-lock map (" + filePath + ")")
			}
			p.resourceLocks.Delete(*resource)
		}

		// Open lock
		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: createResourceLock() - resource lock barrier open() (" + filePath + ")")
		}
		newLock.Open()
	}), nil
}

// ReadFileAtomic reads a file atomically
func (p *DiskFileSystemProvider) ReadFileAtomic(resource *baseCommon.URI, opts *platformFilesCommon.IFileAtomicReadOptions) ([]byte, error) {
	var lock baseCommon.IDisposable
	var err error

	if opts != nil && opts.Atomic {
		if p.traceResourceLocks {
			p.logService.Trace("[Disk FileSystemProvider]: atomic read operation started (" + p.ToFilePath(resource) + ")")
		}

		// When the read should be atomic, make sure
		// to await any pending locks for the resource
		// and lock for the duration of the read.
		lock, err = p.createResourceLock(resource)
		if err != nil {
			return nil, err
		}
		defer lock.Dispose()
	}

	return p.ReadFile(resource)
}

// WriteFileAtomic writes a file atomically
func (p *DiskFileSystemProvider) WriteFileAtomic(resource *baseCommon.URI, content []byte, opts platformFilesCommon.IFileWriteOptions) error {
	canWriteAtomic, err := p.canWriteFileAtomic(resource)
	if err != nil {
		return err
	}

	if canWriteAtomic && opts.Atomic != nil {
		if atomicOpts, ok := opts.Atomic.(platformFilesCommon.IFileAtomicOptions); ok && atomicOpts.Postfix != "" {
			resourceDir := baseCommon.NewURI("file", "", filepath.Dir(p.ToFilePath(resource)), "", "")
			resourceBase := filepath.Base(p.ToFilePath(resource))
			tempResource := baseCommon.JoinPath(resourceDir, resourceBase+atomicOpts.Postfix)
			return p.doWriteFileAtomic(resource, tempResource, content, opts)
		}
	}

	return p.WriteFile(resource, content, opts)
}

// canWriteFileAtomic checks if a file can be written atomically
func (p *DiskFileSystemProvider) canWriteFileAtomic(resource *baseCommon.URI) (bool, error) {
	filePath := p.ToFilePath(resource)
	info, err := os.Lstat(filePath)
	if err != nil {
		// If file doesn't exist, we can write atomically
		if os.IsNotExist(err) {
			return true, nil
		}
		return false, err
	}

	// atomic writes are unsupported for symbolic links because
	// we need to ensure that the `rename` operation is atomic
	// and that only works if the link is on the same disk.
	if info.Mode()&os.ModeSymlink != 0 {
		return false, nil
	}

	return true, nil
}

// doWriteFileAtomic performs atomic write using temporary file
func (p *DiskFileSystemProvider) doWriteFileAtomic(resource *baseCommon.URI, tempResource *baseCommon.URI, content []byte, opts platformFilesCommon.IFileWriteOptions) error {
	// Create locks for all resources involved
	locks := baseCommon.NewDisposableStore()
	defer locks.Dispose()

	// Lock the main resource
	lock1, err := p.createResourceLock(resource)
	if err != nil {
		return err
	}
	locks.Add(lock1)

	// Lock the temporary resource
	lock2, err := p.createResourceLock(tempResource)
	if err != nil {
		return err
	}
	locks.Add(lock2)

	// Write to temp resource first
	err = p.WriteFile(tempResource, content, opts)
	if err != nil {
		return err
	}

	// Rename over existing to ensure atomic replace
	err = p.Rename(tempResource, resource, platformFilesCommon.IFileOverwriteOptions{Overwrite: true})
	if err != nil {
		// Cleanup in case of rename error
		p.Delete(tempResource, platformFilesCommon.IFileDeleteOptions{
			Recursive: false,
			UseTrash:  false,
		})
		return err
	}

	return nil
}

// normalizePos normalizes file position for handle operations
func (p *DiskFileSystemProvider) normalizePos(fd int, pos int64) *int64 {
	p.handleMutex.RLock()
	defer p.handleMutex.RUnlock()

	if pos == -1 {
		// Use current position
		if currentPos, exists := p.mapHandleToPos[fd]; exists {
			return &currentPos
		}
		return nil
	}

	return &pos
}

// updatePos updates the position for a file handle
func (p *DiskFileSystemProvider) updatePos(fd int, pos *int64, bytesLength int64) {
	p.handleMutex.Lock()
	defer p.handleMutex.Unlock()

	if pos != nil {
		p.mapHandleToPos[fd] = *pos + bytesLength
	}
}
