/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package watcher

import (
	"fmt"
	"strings"

	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher/nodejs"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher/parcel"
)

// ComputeStats generates statistics about watcher usage
func ComputeStats(
	requests []platformFilesCommon.IUniversalWatchRequest,
	failedRecursiveRequests int,
	recursiveWatcher *parcel.ParcelWatcher,
	nonRecursiveWatcher *nodejs.NodeJSWatcher,
) string {
	lines := make([]string, 0)

	// Separate recursive and non-recursive requests
	allRecursiveRequests := make([]platformFilesCommon.IUniversalWatchRequest, 0)
	allNonRecursiveRequests := make([]platformFilesCommon.IUniversalWatchRequest, 0)

	for _, request := range requests {
		if request.GetRecursive() {
			allRecursiveRequests = append(allRecursiveRequests, request)
		} else {
			allNonRecursiveRequests = append(allNonRecursiveRequests, request)
		}
	}

	// Sort by path prefix for better readability
	allRecursiveRequests = sortByPathPrefix(allRecursiveRequests)
	allNonRecursiveRequests = sortByPathPrefix(allNonRecursiveRequests)

	// Compute statistics
	recursiveRequestsStatus := computeRequestStatus(allRecursiveRequests)
	recursiveWatcherStatus := computeRecursiveWatchStatus(recursiveWatcher)

	nonRecursiveRequestsStatus := computeRequestStatus(allNonRecursiveRequests)
	nonRecursiveWatcherStatus := computeNonRecursiveWatchStatus(nonRecursiveWatcher)

	// Build summary
	lines = append(lines, "[Summary]")
	lines = append(lines, fmt.Sprintf("- Recursive Requests:     total: %d, suspended: %d, polling: %d, failed: %d",
		len(allRecursiveRequests), recursiveRequestsStatus.suspended, recursiveRequestsStatus.polling, failedRecursiveRequests))
	lines = append(lines, fmt.Sprintf("- Non-Recursive Requests: total: %d, suspended: %d, polling: %d",
		len(allNonRecursiveRequests), nonRecursiveRequestsStatus.suspended, nonRecursiveRequestsStatus.polling))
	lines = append(lines, fmt.Sprintf("- Recursive Watchers:     total: %d, active: %d, failed: %d, stopped: %d",
		recursiveWatcherStatus.total, recursiveWatcherStatus.active, recursiveWatcherStatus.failed, recursiveWatcherStatus.stopped))
	lines = append(lines, fmt.Sprintf("- Non-Recursive Watchers: total: %d, active: %d, failed: %d, reusing: %d",
		nonRecursiveWatcherStatus.total, nonRecursiveWatcherStatus.active, nonRecursiveWatcherStatus.failed, nonRecursiveWatcherStatus.reusing))
	lines = append(lines, fmt.Sprintf("- I/O Handles Impact:     total: %d",
		recursiveRequestsStatus.polling+nonRecursiveRequestsStatus.polling+recursiveWatcherStatus.active+nonRecursiveWatcherStatus.active))

	// Add detailed request information
	lines = append(lines, fmt.Sprintf("\n[Recursive Requests (%d, suspended: %d, polling: %d)]:",
		len(allRecursiveRequests), recursiveRequestsStatus.suspended, recursiveRequestsStatus.polling))

	recursiveRequestLines := make([]string, 0)
	for _, request := range allRecursiveRequests {
		fillRequestStats(&recursiveRequestLines, request)
	}
	lines = append(lines, alignTextColumns(recursiveRequestLines)...)

	// Add recursive watcher details
	recursiveWatcherLines := make([]string, 0)
	fillRecursiveWatcherStats(&recursiveWatcherLines, recursiveWatcher)
	lines = append(lines, alignTextColumns(recursiveWatcherLines)...)

	// Add non-recursive request information
	lines = append(lines, fmt.Sprintf("\n[Non-Recursive Requests (%d, suspended: %d, polling: %d)]:",
		len(allNonRecursiveRequests), nonRecursiveRequestsStatus.suspended, nonRecursiveRequestsStatus.polling))

	nonRecursiveRequestLines := make([]string, 0)
	for _, request := range allNonRecursiveRequests {
		fillRequestStats(&nonRecursiveRequestLines, request)
	}
	lines = append(lines, alignTextColumns(nonRecursiveRequestLines)...)

	// Add non-recursive watcher details
	nonRecursiveWatcherLines := make([]string, 0)
	fillNonRecursiveWatcherStats(&nonRecursiveWatcherLines, nonRecursiveWatcher)
	lines = append(lines, alignTextColumns(nonRecursiveWatcherLines)...)

	return fmt.Sprintf("\n\n[File Watcher] request stats:\n\n%s\n\n", strings.Join(lines, "\n"))
}

// RequestStatus represents the status of watch requests
type RequestStatus struct {
	suspended int
	polling   int
}

// WatcherStatus represents the status of watchers
type WatcherStatus struct {
	total   int
	active  int
	failed  int
	stopped int
	reusing int
}

// sortByPathPrefix sorts requests by path prefix for better readability
func sortByPathPrefix(requests []platformFilesCommon.IUniversalWatchRequest) []platformFilesCommon.IUniversalWatchRequest {
	// Simple implementation - in a real scenario you might want to implement proper sorting
	return requests
}

// computeRequestStatus computes statistics for watch requests
func computeRequestStatus(requests []platformFilesCommon.IUniversalWatchRequest) RequestStatus {
	// Simplified implementation - in a real scenario you would check actual suspension status
	return RequestStatus{
		suspended: 0,
		polling:   0,
	}
}

// computeRecursiveWatchStatus computes statistics for recursive watchers
func computeRecursiveWatchStatus(recursiveWatcher *parcel.ParcelWatcher) WatcherStatus {
	// Simplified implementation - in a real scenario you would inspect actual watcher state
	if recursiveWatcher == nil {
		return WatcherStatus{}
	}

	return WatcherStatus{
		total:   1, // Simplified - would count actual watchers
		active:  1,
		failed:  0,
		stopped: 0,
	}
}

// computeNonRecursiveWatchStatus computes statistics for non-recursive watchers
func computeNonRecursiveWatchStatus(nonRecursiveWatcher *nodejs.NodeJSWatcher) WatcherStatus {
	// Simplified implementation - in a real scenario you would inspect actual watcher state
	if nonRecursiveWatcher == nil {
		return WatcherStatus{}
	}

	return WatcherStatus{
		total:   1, // Simplified - would count actual watchers
		active:  1,
		failed:  0,
		stopped: 0,
		reusing: 0,
	}
}

// alignTextColumns aligns text in columns for better readability
func alignTextColumns(lines []string) []string {
	if len(lines) == 0 {
		return lines
	}

	// Find the maximum length of the first column
	maxLength := 0
	for _, line := range lines {
		parts := strings.Split(line, "\t")
		if len(parts) > 0 && len(parts[0]) > maxLength {
			maxLength = len(parts[0])
		}
	}

	// Align columns
	for i, line := range lines {
		parts := strings.Split(line, "\t")
		if len(parts) == 2 {
			padding := strings.Repeat(" ", maxLength-len(parts[0]))
			lines[i] = fmt.Sprintf("%s%s\t%s", parts[0], padding, parts[1])
		}
	}

	return lines
}

// fillRequestStats fills request statistics lines
func fillRequestStats(lines *[]string, request platformFilesCommon.IUniversalWatchRequest) {
	decorations := make([]string, 0)

	// In a real implementation, you would check if the request is suspended
	// For now, we'll just show basic information

	*lines = append(*lines, fmt.Sprintf(" %s\t%s(%s)",
		request.GetPath(),
		strings.Join(decorations, " "),
		requestDetailsToString(request)))
}

// fillRecursiveWatcherStats fills recursive watcher statistics
func fillRecursiveWatcherStats(lines *[]string, recursiveWatcher *parcel.ParcelWatcher) {
	if recursiveWatcher == nil {
		return
	}

	status := computeRecursiveWatchStatus(recursiveWatcher)
	*lines = append(*lines, fmt.Sprintf("\n[Recursive Watchers (%d, active: %d, failed: %d, stopped: %d)]:",
		status.total, status.active, status.failed, status.stopped))

	// In a real implementation, you would iterate through actual watchers
	*lines = append(*lines, " <simplified implementation>")
}

// fillNonRecursiveWatcherStats fills non-recursive watcher statistics
func fillNonRecursiveWatcherStats(lines *[]string, nonRecursiveWatcher *nodejs.NodeJSWatcher) {
	if nonRecursiveWatcher == nil {
		return
	}

	status := computeNonRecursiveWatchStatus(nonRecursiveWatcher)
	*lines = append(*lines, fmt.Sprintf("\n[Non-Recursive Watchers (%d, active: %d, failed: %d, reusing: %d)]:",
		status.total, status.active, status.failed, status.reusing))

	// In a real implementation, you would iterate through actual watchers
	*lines = append(*lines, " <simplified implementation>")
}

// requestDetailsToString converts request details to a string
func requestDetailsToString(request platformFilesCommon.IUniversalWatchRequest) string {
	excludes := request.GetExcludes()
	excludesStr := "<none>"
	if len(excludes) > 0 {
		excludesStr = strings.Join(excludes, ",")
	}

	includes := request.GetIncludes()
	includesStr := "<all>"
	if len(includes) > 0 {
		includesStr = fmt.Sprintf("%v", includes)
	}

	filterStr := "<none>"
	if filter := request.GetFilter(); filter != nil {
		filterStr = fmt.Sprintf("%v", *filter)
	}

	correlationStr := "<none>"
	if cid := request.GetCorrelationId(); cid != nil {
		correlationStr = fmt.Sprintf("%d", *cid)
	}

	return fmt.Sprintf("excludes: %s, includes: %s, filter: %s, correlationId: %s",
		excludesStr, includesStr, filterStr, correlationStr)
}
