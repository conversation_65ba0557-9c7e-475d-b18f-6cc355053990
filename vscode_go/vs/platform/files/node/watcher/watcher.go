package watcher

import (
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	nodeJSWatcher "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher/nodejs"
	parcelWatcher "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher/parcel"
)

// simpleWatchRequest is a simple implementation of IUniversalWatchRequest
type simpleWatchRequest struct {
	path          string
	recursive     bool
	excludes      []string
	includes      []interface{}
	correlationId *int
	filter        *platformFilesCommon.FileChangeFilter
}

func (r *simpleWatchRequest) GetPath() string                                  { return r.path }
func (r *simpleWatchRequest) GetRecursive() bool                               { return r.recursive }
func (r *simpleWatchRequest) GetExcludes() []string                            { return r.excludes }
func (r *simpleWatchRequest) GetIncludes() []interface{}                       { return r.includes }
func (r *simpleWatchRequest) GetCorrelationId() *int                           { return r.correlationId }
func (r *simpleWatchRequest) GetFilter() *platformFilesCommon.FileChangeFilter { return r.filter }

// UniversalWatcherClient implements a universal file watcher client
type UniversalWatcherClient struct {
	*platformFilesCommon.AbstractUniversalWatcherClient

	// File system watcher
	fsWatcher *fsnotify.Watcher

	// Watched paths
	watchedPaths map[string]platformFilesCommon.IUniversalWatchRequest
	pathMutex    sync.RWMutex

	// Event coalescing
	eventCoalescer    *platformFilesCommon.EventCoalescer
	flushDelayer      *baseCommon.ThrottledDelayer[interface{}]
	eventAggregator   map[string]*platformFilesCommon.IFileChange
	eventAggregatorMu sync.Mutex

	// Configuration
	pollingWatcher map[string]*time.Ticker
	pollingMutex   sync.RWMutex

	// Event emitters
	onDidChangeFileEmitter *baseCommon.Emitter[[]platformFilesCommon.IFileChange]
	onDidLogMessageEmitter *baseCommon.Emitter[platformFilesCommon.ILogMessage]
	verboseLoggingEnabled  bool
}

// NewUniversalWatcherClient creates a new universal watcher client
func NewUniversalWatcherClient(
	onChange func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *UniversalWatcherClient {
	c := &UniversalWatcherClient{
		AbstractUniversalWatcherClient: platformFilesCommon.NewAbstractUniversalWatcherClient(onChange, onLogMessage, verboseLogging),
		watchedPaths:                   make(map[string]platformFilesCommon.IUniversalWatchRequest),
		eventCoalescer:                 platformFilesCommon.NewEventCoalescer(),
		flushDelayer:                   baseCommon.NewThrottledDelayer[interface{}](75 * time.Millisecond),
		eventAggregator:                make(map[string]*platformFilesCommon.IFileChange),
		pollingWatcher:                 make(map[string]*time.Ticker),
		onDidChangeFileEmitter:         baseCommon.NewEmitter[[]platformFilesCommon.IFileChange](),
		onDidLogMessageEmitter:         baseCommon.NewEmitter[platformFilesCommon.ILogMessage](),
		verboseLoggingEnabled:          verboseLogging,
	}

	// Initialize fsnotify watcher
	var err error
	c.fsWatcher, err = fsnotify.NewWatcher()
	if err != nil {
		c.warn("Failed to create fsnotify watcher: " + err.Error())
	} else {
		go c.watchFsNotifyEvents()
	}

	// Subscribe to events
	c.onDidChangeFileEmitter.Event().Subscribe(func(changes []platformFilesCommon.IFileChange) {
		onChange(changes)
	})
	c.onDidLogMessageEmitter.Event().Subscribe(func(msg platformFilesCommon.ILogMessage) {
		onLogMessage(msg)
	})

	return c
}

// UniversalWatcher implements the main watcher class
type UniversalWatcher struct {
	*baseCommon.Disposable

	recursiveWatcher    *parcelWatcher.ParcelWatcher
	nonRecursiveWatcher *nodeJSWatcher.NodeJSWatcher

	onDidChangeFileEmitter *baseCommon.Emitter[[]platformFilesCommon.IFileChange]
	onDidErrorEmitter      *baseCommon.Emitter[platformFilesCommon.IWatcherErrorEvent]
	onDidLogMessageEmitter *baseCommon.Emitter[platformFilesCommon.ILogMessage]

	requests                []platformFilesCommon.IUniversalWatchRequest
	failedRecursiveRequests int
}

// NewUniversalWatcher creates a new universal watcher
func NewUniversalWatcher() *UniversalWatcher {
	uw := &UniversalWatcher{
		Disposable:              baseCommon.NewDisposable(),
		recursiveWatcher:        parcelWatcher.NewParcelWatcher(),
		onDidChangeFileEmitter:  baseCommon.NewEmitter[[]platformFilesCommon.IFileChange](),
		onDidErrorEmitter:       baseCommon.NewEmitter[platformFilesCommon.IWatcherErrorEvent](),
		onDidLogMessageEmitter:  baseCommon.NewEmitter[platformFilesCommon.ILogMessage](),
		requests:                make([]platformFilesCommon.IUniversalWatchRequest, 0),
		failedRecursiveRequests: 0,
	}

	// Initialize nonRecursiveWatcher with recursiveWatcher
	uw.nonRecursiveWatcher = nodeJSWatcher.NewNodeJSWatcher(uw.recursiveWatcher)

	// Register for disposal
	uw.Register(uw.recursiveWatcher)
	uw.Register(uw.nonRecursiveWatcher)
	uw.Register(uw.onDidChangeFileEmitter)
	uw.Register(uw.onDidErrorEmitter)
	uw.Register(uw.onDidLogMessageEmitter)

	// Set up error handling
	uw.recursiveWatcher.OnDidError().Subscribe(func(e platformFilesCommon.IWatcherErrorEvent) {
		if e.Request != nil {
			uw.failedRecursiveRequests++
		}
		uw.onDidErrorEmitter.Fire(e)
	})

	uw.nonRecursiveWatcher.OnDidError().Subscribe(func(e platformFilesCommon.IWatcherErrorEvent) {
		uw.onDidErrorEmitter.Fire(e)
	})

	return uw
}

// OnDidChangeFile returns the file change event
func (uw *UniversalWatcher) OnDidChangeFile() baseCommon.Event[[]platformFilesCommon.IFileChange] {
	// Combine events from both watchers
	recursiveEvents := uw.recursiveWatcher.OnDidChangeFile()
	nonRecursiveEvents := uw.nonRecursiveWatcher.OnDidChangeFile()

	// Create combined emitter
	combinedEmitter := baseCommon.NewEmitter[[]platformFilesCommon.IFileChange]()

	recursiveEvents.Subscribe(func(changes []platformFilesCommon.IFileChange) {
		combinedEmitter.Fire(changes)
	})

	nonRecursiveEvents.Subscribe(func(changes []platformFilesCommon.IFileChange) {
		combinedEmitter.Fire(changes)
	})

	return combinedEmitter.Event()
}

// OnDidError returns the error event
func (uw *UniversalWatcher) OnDidError() baseCommon.Event[platformFilesCommon.IWatcherErrorEvent] {
	return uw.onDidErrorEmitter.Event()
}

// OnDidLogMessage returns the log message event
func (uw *UniversalWatcher) OnDidLogMessage() baseCommon.Event[platformFilesCommon.ILogMessage] {
	// Combine events from both watchers and own messages
	recursiveEvents := uw.recursiveWatcher.OnDidLogMessage()
	nonRecursiveEvents := uw.nonRecursiveWatcher.OnDidLogMessage()

	// Create combined emitter
	combinedEmitter := baseCommon.NewEmitter[platformFilesCommon.ILogMessage]()

	recursiveEvents.Subscribe(func(msg platformFilesCommon.ILogMessage) {
		combinedEmitter.Fire(msg)
	})

	nonRecursiveEvents.Subscribe(func(msg platformFilesCommon.ILogMessage) {
		combinedEmitter.Fire(msg)
	})

	uw.onDidLogMessageEmitter.Event().Subscribe(func(msg platformFilesCommon.ILogMessage) {
		combinedEmitter.Fire(msg)
	})

	return combinedEmitter.Event()
}

// Watch starts watching the provided requests (implements IWatcher interface)
func (uw *UniversalWatcher) Watch(requests []platformFilesCommon.IWatchRequest) error {
	// Convert generic watch requests to universal ones
	var universalRequests []platformFilesCommon.IUniversalWatchRequest
	for _, request := range requests {
		// Convert IWatchRequest to IUniversalWatchRequest
		universalRequests = append(universalRequests, &simpleWatchRequest{
			path:          request.Path,
			recursive:     request.Recursive,
			excludes:      request.Excludes,
			includes:      request.Includes,
			correlationId: request.CorrelationId,
			filter:        request.Filter,
		})
	}
	return uw.WatchUniversal(universalRequests)
}

// WatchUniversal starts watching the provided universal requests
func (uw *UniversalWatcher) WatchUniversal(requests []platformFilesCommon.IUniversalWatchRequest) error {
	uw.requests = requests
	uw.failedRecursiveRequests = 0

	// Convert universal requests to specific types
	var recursiveRequests []platformFilesCommon.IRecursiveWatchRequest
	var nonRecursiveRequests []platformFilesCommon.INonRecursiveWatchRequest

	for _, request := range requests {
		if request.GetRecursive() {
			recursiveRequests = append(recursiveRequests, platformFilesCommon.IRecursiveWatchRequest{
				IWatchRequest: platformFilesCommon.IWatchRequest{
					Path:          request.GetPath(),
					Recursive:     request.GetRecursive(),
					Excludes:      request.GetExcludes(),
					Includes:      request.GetIncludes(),
					CorrelationId: request.GetCorrelationId(),
					Filter:        request.GetFilter(),
				},
				Recursive: true,
			})
		} else {
			nonRecursiveRequests = append(nonRecursiveRequests, platformFilesCommon.INonRecursiveWatchRequest{
				IWatchRequest: platformFilesCommon.IWatchRequest{
					Path:          request.GetPath(),
					Recursive:     request.GetRecursive(),
					Excludes:      request.GetExcludes(),
					Includes:      request.GetIncludes(),
					CorrelationId: request.GetCorrelationId(),
					Filter:        request.GetFilter(),
				},
				Recursive: false,
			})
		}
	}

	// Watch recursively first
	var err error
	if len(recursiveRequests) > 0 {
		err = uw.recursiveWatcher.Watch(recursiveRequests)
	}

	// Then watch non-recursively
	if len(nonRecursiveRequests) > 0 {
		nonRecursiveErr := uw.nonRecursiveWatcher.WatchNonRecursive(nonRecursiveRequests)
		if err == nil {
			err = nonRecursiveErr
		}
	}

	return err
}

// SetVerboseLogging sets verbose logging
func (uw *UniversalWatcher) SetVerboseLogging(enabled bool) error {
	// Log stats if enabled
	if enabled && len(uw.requests) > 0 {
		uw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: "Universal watcher stats",
		})
	}

	// Forward to watchers
	var recursiveErr error
	var nonRecursiveErr error

	if uw.recursiveWatcher != nil {
		recursiveErr = uw.recursiveWatcher.SetVerboseLogging(enabled)
	}

	if uw.nonRecursiveWatcher != nil {
		nonRecursiveErr = uw.nonRecursiveWatcher.SetVerboseLogging(enabled)
	}

	if recursiveErr != nil {
		return recursiveErr
	}
	return nonRecursiveErr
}

// Stop stops all watchers
func (uw *UniversalWatcher) Stop() error {
	var recursiveErr error
	var nonRecursiveErr error

	if uw.recursiveWatcher != nil {
		recursiveErr = uw.recursiveWatcher.Stop()
	}

	if uw.nonRecursiveWatcher != nil {
		nonRecursiveErr = uw.nonRecursiveWatcher.Stop()
	}

	if recursiveErr != nil {
		return recursiveErr
	}
	return nonRecursiveErr
}

// Watch implements AbstractUniversalWatcherClient
func (c *UniversalWatcherClient) Watch(requests []platformFilesCommon.IUniversalWatchRequest) error {
	c.trace("received watch request for " + string(rune(len(requests))) + " paths")

	// Stop existing watchers
	c.stopWatching()

	// Start watching new requests
	for _, request := range requests {
		c.doWatch(request)
	}

	return nil
}

// doWatch performs watching for a single request
func (c *UniversalWatcherClient) doWatch(request platformFilesCommon.IUniversalWatchRequest) {
	path := request.GetPath()

	c.pathMutex.Lock()
	c.watchedPaths[path] = request
	c.pathMutex.Unlock()

	// Check if path exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		c.trace("path does not exist, will watch for creation: " + path)
		c.watchForCreation(request)
		return
	}

	// Determine watching strategy
	if c.shouldUsePolling(request) {
		c.watchWithPolling(request)
	} else {
		c.watchWithFsNotify(request)
	}
}

// shouldUsePolling determines if polling should be used for a request
func (c *UniversalWatcherClient) shouldUsePolling(request platformFilesCommon.IUniversalWatchRequest) bool {
	// Check if request explicitly requires polling by examining the concrete type
	// Since IUniversalWatchRequest is an interface, we need to check if the underlying
	// implementation has polling interval set
	if request.GetRecursive() {
		// For recursive requests, check if they need polling
		// This would be determined by the specific implementation
		// For now, use fsnotify by default unless unavailable
	}

	// Use fsnotify by default if available
	return c.fsWatcher == nil
}

// watchWithFsNotify watches using fsnotify
func (c *UniversalWatcherClient) watchWithFsNotify(request platformFilesCommon.IUniversalWatchRequest) {
	if c.fsWatcher == nil {
		c.watchWithPolling(request)
		return
	}

	path := request.GetPath()
	c.trace("watching with fsnotify: " + path)

	// Add to fsnotify watcher
	err := c.fsWatcher.Add(path)
	if err != nil {
		c.warn("Failed to add path to fsnotify watcher: " + path + " - " + err.Error())
		c.watchWithPolling(request)
		return
	}

	// For recursive watching, we need to walk the directory tree
	if request.GetRecursive() {
		c.addRecursivePaths(path, request)
	}
}

// addRecursivePaths adds all subdirectories for recursive watching
func (c *UniversalWatcherClient) addRecursivePaths(rootPath string, request platformFilesCommon.IUniversalWatchRequest) {
	err := filepath.Walk(rootPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // Continue walking even if we hit an error
		}

		if info.IsDir() && path != rootPath {
			// Check if path should be excluded
			if c.shouldExcludePath(path, request) {
				return filepath.SkipDir
			}

			// Add directory to watcher
			if addErr := c.fsWatcher.Add(path); addErr != nil {
				c.trace("Failed to add recursive path: " + path + " - " + addErr.Error())
			}
		}
		return nil
	})

	if err != nil {
		c.warn("Error walking directory tree for " + rootPath + ": " + err.Error())
	}
}

// shouldExcludePath checks if a path should be excluded based on request filters
func (c *UniversalWatcherClient) shouldExcludePath(path string, request platformFilesCommon.IUniversalWatchRequest) bool {
	excludes := request.GetExcludes()
	if len(excludes) == 0 {
		return false
	}

	for _, exclude := range excludes {
		// Simple pattern matching - in a real implementation would use proper glob matching
		if strings.Contains(path, exclude) {
			return true
		}
	}
	return false
}

// watchWithPolling watches using polling
func (c *UniversalWatcherClient) watchWithPolling(request platformFilesCommon.IUniversalWatchRequest) {
	path := request.GetPath()

	// Default polling interval
	interval := 5 * time.Second

	// For now, use default interval since we can't access PollingInterval from interface
	// In a real implementation, this would need to be redesigned to support polling hints

	c.trace("watching with polling (interval: " + interval.String() + "): " + path)

	// Create polling ticker
	ticker := time.NewTicker(interval)

	c.pollingMutex.Lock()
	c.pollingWatcher[path] = ticker
	c.pollingMutex.Unlock()

	// Start polling in a goroutine
	go c.pollPath(path, request, ticker)
}

// pollPath polls a path for changes
func (c *UniversalWatcherClient) pollPath(path string, request platformFilesCommon.IUniversalWatchRequest, ticker *time.Ticker) {
	defer ticker.Stop()

	var lastStat os.FileInfo
	if stat, err := os.Stat(path); err == nil {
		lastStat = stat
	}

	for range ticker.C {
		// Check if this path is still being watched
		c.pathMutex.RLock()
		_, exists := c.watchedPaths[path]
		c.pathMutex.RUnlock()

		if !exists {
			return // Stop polling
		}

		// Check for changes
		currentStat, err := os.Stat(path)
		if err != nil {
			if !os.IsNotExist(err) {
				c.warn("Error polling path " + path + ": " + err.Error())
			}
			// Path was deleted
			if lastStat != nil {
				c.emitFileChange(&platformFilesCommon.IFileChange{
					Resource: baseCommon.File(path),
					Type:     platformFilesCommon.FileChangeTypeDELETED,
					CId:      request.GetCorrelationId(),
				})
				lastStat = nil
			}
			continue
		}

		// Path was created
		if lastStat == nil {
			c.emitFileChange(&platformFilesCommon.IFileChange{
				Resource: baseCommon.File(path),
				Type:     platformFilesCommon.FileChangeTypeADDED,
				CId:      request.GetCorrelationId(),
			})
			lastStat = currentStat
			continue
		}

		// Path was modified
		if currentStat.ModTime() != lastStat.ModTime() || currentStat.Size() != lastStat.Size() {
			c.emitFileChange(&platformFilesCommon.IFileChange{
				Resource: baseCommon.File(path),
				Type:     platformFilesCommon.FileChangeTypeUPDATED,
				CId:      request.GetCorrelationId(),
			})
			lastStat = currentStat
		}
	}
}

// watchForCreation watches for path creation using parent directory monitoring
func (c *UniversalWatcherClient) watchForCreation(request platformFilesCommon.IUniversalWatchRequest) {
	path := request.GetPath()
	parentDir := filepath.Dir(path)

	c.trace("watching for creation: " + path + " (monitoring parent: " + parentDir + ")")

	// Watch parent directory if it exists
	if _, err := os.Stat(parentDir); err == nil {
		// Create a temporary request for parent directory monitoring
		// Since we need IUniversalWatchRequest, create a simple wrapper
		parentRequest := &simpleWatchRequest{
			path:          parentDir,
			recursive:     false,
			excludes:      []string{},
			includes:      nil,
			correlationId: request.GetCorrelationId(),
			filter:        request.GetFilter(),
		}

		c.watchWithFsNotify(parentRequest)
	} else {
		// Parent doesn't exist either, use polling
		c.watchWithPolling(request)
	}
}

// watchFsNotifyEvents watches for fsnotify events
func (c *UniversalWatcherClient) watchFsNotifyEvents() {
	for {
		select {
		case event, ok := <-c.fsWatcher.Events:
			if !ok {
				return
			}
			c.handleFsNotifyEvent(event)

		case err, ok := <-c.fsWatcher.Errors:
			if !ok {
				return
			}
			c.warn("fsnotify error: " + err.Error())
		}
	}
}

// handleFsNotifyEvent handles a single fsnotify event
func (c *UniversalWatcherClient) handleFsNotifyEvent(event fsnotify.Event) {
	var changeType platformFilesCommon.FileChangeType

	switch {
	case event.Op&fsnotify.Create == fsnotify.Create:
		changeType = platformFilesCommon.FileChangeTypeADDED
	case event.Op&fsnotify.Remove == fsnotify.Remove:
		changeType = platformFilesCommon.FileChangeTypeDELETED
	case event.Op&fsnotify.Write == fsnotify.Write:
		changeType = platformFilesCommon.FileChangeTypeUPDATED
	case event.Op&fsnotify.Rename == fsnotify.Rename:
		// Treat rename as delete + add
		changeType = platformFilesCommon.FileChangeTypeDELETED
	default:
		return // Ignore other events
	}

	// Find matching request
	var correlationId *int
	c.pathMutex.RLock()
	for watchedPath, request := range c.watchedPaths {
		if c.pathMatches(event.Name, watchedPath, request) {
			correlationId = request.GetCorrelationId()
			break
		}
	}
	c.pathMutex.RUnlock()

	c.emitFileChange(&platformFilesCommon.IFileChange{
		Resource: baseCommon.File(event.Name),
		Type:     changeType,
		CId:      correlationId,
	})

	// Handle directory creation for recursive watching
	if changeType == platformFilesCommon.FileChangeTypeADDED {
		if stat, err := os.Stat(event.Name); err == nil && stat.IsDir() {
			c.handleDirectoryCreated(event.Name)
		}
	}
}

// pathMatches checks if an event path matches a watched path
func (c *UniversalWatcherClient) pathMatches(eventPath, watchedPath string, request platformFilesCommon.IUniversalWatchRequest) bool {
	if eventPath == watchedPath {
		return true
	}

	if request.GetRecursive() {
		// Check if event path is under watched path
		return strings.HasPrefix(eventPath, watchedPath+string(os.PathSeparator))
	}

	return false
}

// handleDirectoryCreated handles when a new directory is created
func (c *UniversalWatcherClient) handleDirectoryCreated(dirPath string) {
	c.pathMutex.RLock()
	defer c.pathMutex.RUnlock()

	for watchedPath, request := range c.watchedPaths {
		if request.GetRecursive() && strings.HasPrefix(dirPath, watchedPath+string(os.PathSeparator)) {
			// Add new directory to watcher
			if c.fsWatcher != nil {
				if err := c.fsWatcher.Add(dirPath); err != nil {
					c.trace("Failed to add new directory to watcher: " + dirPath + " - " + err.Error())
				} else {
					c.trace("Added new directory to recursive watcher: " + dirPath)
				}
			}
			break
		}
	}
}

// emitFileChange emits a file change event with coalescing
func (c *UniversalWatcherClient) emitFileChange(change *platformFilesCommon.IFileChange) {
	c.eventAggregatorMu.Lock()

	// Coalesce events
	c.eventCoalescer.ProcessEvent(*change)

	// Store in aggregator
	key := change.Resource.GetFSPath()
	c.eventAggregator[key] = change

	c.eventAggregatorMu.Unlock()

	// Trigger flush with delay
	c.flushDelayer.Trigger(func() <-chan interface{} {
		result := make(chan interface{}, 1)
		go func() {
			c.flushEvents()
			result <- nil
		}()
		return result
	}, 75*time.Millisecond)
}

// flushEvents flushes accumulated events
func (c *UniversalWatcherClient) flushEvents() {
	c.eventAggregatorMu.Lock()

	// Get coalesced events
	events := c.eventCoalescer.Coalesce()

	// Clear aggregator
	c.eventAggregator = make(map[string]*platformFilesCommon.IFileChange)

	c.eventAggregatorMu.Unlock()

	if len(events) > 0 {
		c.trace("flushing " + strconv.Itoa(len(events)) + " coalesced file change events")
		c.onDidChangeFileEmitter.Fire(events)
	}
}

// stopWatching stops all watching
func (c *UniversalWatcherClient) stopWatching() {
	c.pathMutex.Lock()
	defer c.pathMutex.Unlock()

	// Clear watched paths
	c.watchedPaths = make(map[string]platformFilesCommon.IUniversalWatchRequest)

	// Stop polling watchers
	c.pollingMutex.Lock()
	for path, ticker := range c.pollingWatcher {
		ticker.Stop()
		delete(c.pollingWatcher, path)
	}
	c.pollingMutex.Unlock()

	// Reset fsnotify watcher
	if c.fsWatcher != nil {
		c.fsWatcher.Close()
		var err error
		c.fsWatcher, err = fsnotify.NewWatcher()
		if err != nil {
			c.warn("Failed to recreate fsnotify watcher: " + err.Error())
		} else {
			go c.watchFsNotifyEvents()
		}
	}
}

// Dispose implements IDisposable
func (c *UniversalWatcherClient) Dispose() {
	c.stopWatching()
	if c.fsWatcher != nil {
		c.fsWatcher.Close()
	}
	if c.AbstractUniversalWatcherClient != nil {
		c.AbstractUniversalWatcherClient.Dispose()
	}
}

// Override abstract methods from BaseWatcher

// trace implements BaseWatcher.trace
func (c *UniversalWatcherClient) trace(message string) {
	if c.verboseLoggingEnabled {
		c.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: message,
		})
	}
}

// warn implements BaseWatcher.warn
func (c *UniversalWatcherClient) warn(message string) {
	c.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
		Type:    "warn",
		Message: message,
	})
}

// OnDidError implements BaseWatcher.OnDidError
func (c *UniversalWatcherClient) OnDidError() baseCommon.Event[platformFilesCommon.IWatcherErrorEvent] {
	// Create a new emitter for error events
	errorEmitter := baseCommon.NewEmitter[platformFilesCommon.IWatcherErrorEvent]()
	c.Register(errorEmitter)
	return errorEmitter
}
