/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package nodejs

import (
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/fsnotify/fsnotify"
	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	parcelWatcher "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher/parcel"
)

// NodeJSWatcher implements non-recursive file watching
type NodeJSWatcher struct {
	*baseCommon.Disposable

	// Events
	onDidChangeFileEmitter *baseCommon.Emitter[[]platformFilesCommon.IFileChange]
	onDidErrorEmitter      *baseCommon.Emitter[platformFilesCommon.IWatcherErrorEvent]
	onDidLogMessageEmitter *baseCommon.Emitter[platformFilesCommon.ILogMessage]

	// State
	verboseLogging   bool
	watchers         map[string]*fsnotify.Watcher
	watchersMu       sync.RWMutex
	requests         []platformFilesCommon.INonRecursiveWatchRequest
	requestsMu       sync.RWMutex
	recursiveWatcher *parcelWatcher.ParcelWatcher // Reference to recursive watcher for optimization
}

// NewNodeJSWatcher creates a new NodeJSWatcher instance
func NewNodeJSWatcher(recursiveWatcher *parcelWatcher.ParcelWatcher) *NodeJSWatcher {
	nw := &NodeJSWatcher{
		Disposable:             baseCommon.NewDisposable(),
		onDidChangeFileEmitter: baseCommon.NewEmitter[[]platformFilesCommon.IFileChange](),
		onDidErrorEmitter:      baseCommon.NewEmitter[platformFilesCommon.IWatcherErrorEvent](),
		onDidLogMessageEmitter: baseCommon.NewEmitter[platformFilesCommon.ILogMessage](),
		watchers:               make(map[string]*fsnotify.Watcher),
		requests:               make([]platformFilesCommon.INonRecursiveWatchRequest, 0),
		recursiveWatcher:       recursiveWatcher,
	}

	nw.Register(nw.onDidChangeFileEmitter)
	nw.Register(nw.onDidErrorEmitter)
	nw.Register(nw.onDidLogMessageEmitter)

	return nw
}

// OnDidChangeFile returns the file change event
func (nw *NodeJSWatcher) OnDidChangeFile() baseCommon.Event[[]platformFilesCommon.IFileChange] {
	return nw.onDidChangeFileEmitter.Event()
}

// OnDidError returns the error event
func (nw *NodeJSWatcher) OnDidError() baseCommon.Event[platformFilesCommon.IWatcherErrorEvent] {
	return nw.onDidErrorEmitter.Event()
}

// OnDidLogMessage returns the log message event
func (nw *NodeJSWatcher) OnDidLogMessage() baseCommon.Event[platformFilesCommon.ILogMessage] {
	return nw.onDidLogMessageEmitter.Event()
}

// Watch starts watching the provided requests (implements IWatcher interface)
func (nw *NodeJSWatcher) Watch(requests []platformFilesCommon.IWatchRequest) error {
	// Convert generic watch requests to non-recursive ones
	var nonRecursiveRequests []platformFilesCommon.INonRecursiveWatchRequest
	for _, request := range requests {
		nonRecursiveRequests = append(nonRecursiveRequests, platformFilesCommon.INonRecursiveWatchRequest{
			IWatchRequest: request,
			Recursive:     false,
		})
	}
	return nw.WatchNonRecursive(nonRecursiveRequests)
}

// WatchNonRecursive starts watching the provided non-recursive requests
func (nw *NodeJSWatcher) WatchNonRecursive(requests []platformFilesCommon.INonRecursiveWatchRequest) error {
	nw.requestsMu.Lock()
	defer nw.requestsMu.Unlock()

	// Stop existing watchers
	nw.stopAllWatchers()

	// Store new requests
	nw.requests = make([]platformFilesCommon.INonRecursiveWatchRequest, len(requests))
	copy(nw.requests, requests)

	// Start new watchers
	for _, request := range requests {
		if err := nw.startWatcher(request); err != nil {
			nw.onDidErrorEmitter.Fire(platformFilesCommon.IWatcherErrorEvent{
				Error:   err.Error(),
				Request: request,
			})
		}
	}

	return nil
}

// SetVerboseLogging enables or disables verbose logging
func (nw *NodeJSWatcher) SetVerboseLogging(enabled bool) error {
	nw.verboseLogging = enabled
	if enabled {
		nw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: "[NodeJSWatcher] Verbose logging enabled",
		})
	}
	return nil
}

// Stop stops all watchers
func (nw *NodeJSWatcher) Stop() error {
	nw.requestsMu.Lock()
	defer nw.requestsMu.Unlock()

	nw.stopAllWatchers()
	nw.Dispose()
	return nil
}

// startWatcher starts watching a single non-recursive request
func (nw *NodeJSWatcher) startWatcher(request platformFilesCommon.INonRecursiveWatchRequest) error {
	// Check if this path is already covered by a recursive watcher
	if nw.isPathCoveredByRecursiveWatcher(request.GetPath()) {
		if nw.verboseLogging {
			nw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
				Type:    "trace",
				Message: "[NodeJSWatcher] Path already covered by recursive watcher: " + request.GetPath(),
			})
		}
		return nil // No need to create a separate watcher
	}

	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return err
	}

	nw.watchersMu.Lock()
	nw.watchers[request.GetPath()] = watcher
	nw.watchersMu.Unlock()

	// For non-recursive watching, we need to handle files and directories differently
	info, err := os.Stat(request.GetPath())
	if err != nil {
		watcher.Close()
		return err
	}

	if info.IsDir() {
		// For directories, watch the directory itself but not subdirectories
		if err := watcher.Add(request.GetPath()); err != nil {
			watcher.Close()
			return err
		}
	} else {
		// For files, watch the parent directory and filter events
		parentDir := filepath.Dir(request.GetPath())
		if err := watcher.Add(parentDir); err != nil {
			watcher.Close()
			return err
		}
	}

	// Start watching goroutine
	go nw.watchEvents(watcher, request)

	if nw.verboseLogging {
		nw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: "[NodeJSWatcher] Started non-recursive watching: " + request.GetPath(),
		})
	}

	return nil
}

// isPathCoveredByRecursiveWatcher checks if a path is already covered by the recursive watcher
func (nw *NodeJSWatcher) isPathCoveredByRecursiveWatcher(path string) bool {
	if nw.recursiveWatcher == nil {
		return false
	}

	requests := nw.recursiveWatcher.GetRequests()
	for _, recursiveRequest := range requests {
		// Check if the path is under a recursively watched directory
		if nw.isPathUnder(path, recursiveRequest.GetPath()) {
			return true
		}
	}

	return false
}

// isPathUnder checks if childPath is under parentPath
func (nw *NodeJSWatcher) isPathUnder(childPath, parentPath string) bool {
	rel, err := filepath.Rel(parentPath, childPath)
	if err != nil {
		return false
	}
	// If the relative path doesn't start with "..", it's a child path
	return !strings.HasPrefix(rel, "..")
}

// watchEvents handles file system events for a watcher
func (nw *NodeJSWatcher) watchEvents(watcher *fsnotify.Watcher, request platformFilesCommon.INonRecursiveWatchRequest) {
	defer watcher.Close()

	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}
			nw.handleEvent(event, request)

		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}
			nw.onDidErrorEmitter.Fire(platformFilesCommon.IWatcherErrorEvent{
				Error:   err.Error(),
				Request: request,
			})
		}
	}
}

// handleEvent converts fsnotify events to platform file changes
func (nw *NodeJSWatcher) handleEvent(event fsnotify.Event, request platformFilesCommon.INonRecursiveWatchRequest) {
	// For non-recursive watching, filter events to only include direct children
	if !nw.shouldIncludeEvent(event.Name, request) {
		return
	}

	// Convert fsnotify event to file change type
	var changeType platformFilesCommon.FileChangeType
	switch {
	case event.Op&fsnotify.Create == fsnotify.Create:
		changeType = platformFilesCommon.FileChangeTypeADDED
	case event.Op&fsnotify.Write == fsnotify.Write:
		changeType = platformFilesCommon.FileChangeTypeUPDATED
	case event.Op&fsnotify.Remove == fsnotify.Remove:
		changeType = platformFilesCommon.FileChangeTypeDELETED
	case event.Op&fsnotify.Rename == fsnotify.Rename:
		changeType = platformFilesCommon.FileChangeTypeDELETED // Treat rename as delete for simplicity
	default:
		return // Ignore other events
	}

	// Create file change
	resource := baseCommon.NewURI("file", "", event.Name, "", "")
	fileChange := platformFilesCommon.IFileChange{
		Type:     changeType,
		Resource: resource,
		CId:      request.GetCorrelationId(),
	}

	// Emit the change
	nw.onDidChangeFileEmitter.Fire([]platformFilesCommon.IFileChange{fileChange})

	if nw.verboseLogging {
		nw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: "[NodeJSWatcher] File change: " + event.Name + " (" + changeType.String() + ")",
		})
	}
}

// shouldIncludeEvent determines if an event should be included for non-recursive watching
func (nw *NodeJSWatcher) shouldIncludeEvent(eventPath string, request platformFilesCommon.INonRecursiveWatchRequest) bool {
	requestPath := request.GetPath()

	// If watching a specific file, only include events for that file
	if info, err := os.Stat(requestPath); err == nil && !info.IsDir() {
		return eventPath == requestPath
	}

	// If watching a directory, only include direct children (not subdirectories)
	parentDir := filepath.Dir(eventPath)
	return parentDir == requestPath
}

// stopAllWatchers stops and removes all active watchers
func (nw *NodeJSWatcher) stopAllWatchers() {
	nw.watchersMu.Lock()
	defer nw.watchersMu.Unlock()

	for path, watcher := range nw.watchers {
		watcher.Close()
		delete(nw.watchers, path)
	}
}
