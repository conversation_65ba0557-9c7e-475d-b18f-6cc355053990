/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package nodejs

import (
	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	parcelWatcher "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/node/watcher/parcel"
)

// NodeJSWatcherClient implements a client for the non-recursive file watcher
type NodeJSWatcherClient struct {
	*platformFilesCommon.AbstractNonRecursiveWatcherClient
}

// NewNodeJSWatcherClient creates a new NodeJS watcher client
func NewNodeJSWatcherClient(
	onFileChanges func(changes []platformFilesCommon.IFileChange),
	onLogMessage func(msg platformFilesCommon.ILogMessage),
	verboseLogging bool,
) *NodeJSWatcherClient {
	client := &NodeJSWatcherClient{
		AbstractNonRecursiveWatcherClient: platformFilesCommon.NewAbstractNonRecursiveWatcherClient(
			onFileChanges,
			onLogMessage,
			verboseLogging,
		),
	}

	// Initialize the watcher client
	client.init()

	return client
}

// createWatcher creates the underlying watcher implementation
func (c *NodeJSWatcherClient) createWatcher(disposables *baseCommon.DisposableStore) platformFilesCommon.INonRecursiveWatcher {
	// Create the NodeJS watcher with reference to parcel watcher for optimization
	parcelWatcher := parcelWatcher.NewParcelWatcher()
	disposables.Add(parcelWatcher)

	watcher := NewNodeJSWatcher(parcelWatcher)
	disposables.Add(watcher)

	// React to unexpected errors
	watcher.OnDidError().Subscribe(func(err platformFilesCommon.IWatcherErrorEvent) {
		c.OnError("NodeJS watcher error: " + err.Error)
	})

	return watcher
}

// init initializes the watcher client
func (c *NodeJSWatcherClient) init() {
	// Create disposables for lifecycle management
	disposables := baseCommon.NewDisposableStore()
	c.Register(disposables)

	// Create the underlying watcher
	watcher := c.createWatcher(disposables)

	// Connect watcher events to our abstract client
	watcher.OnDidChangeFile().Subscribe(func(changes []platformFilesCommon.IFileChange) {
		c.HandleFileChanges(changes)
	})

	watcher.OnDidLogMessage().Subscribe(func(msg platformFilesCommon.ILogMessage) {
		c.HandleLogMessage(msg)
	})

	// Store the watcher for later use
	c.SetWatcher(watcher)
}
