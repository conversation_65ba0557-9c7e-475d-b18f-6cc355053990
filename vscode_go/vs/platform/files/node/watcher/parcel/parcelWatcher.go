/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package parcel

import (
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/fsnotify/fsnotify"
	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
)

// Pa<PERSON>elWatcher implements recursive file watching using fsnotify
type ParcelWatcher struct {
	*baseCommon.Disposable

	// Events
	onDidChangeFileEmitter *baseCommon.Emitter[[]platformFilesCommon.IFileChange]
	onDidErrorEmitter      *baseCommon.Emitter[platformFilesCommon.IWatcherErrorEvent]
	onDidLogMessageEmitter *baseCommon.Emitter[platformFilesCommon.ILogMessage]

	// State
	verboseLogging bool
	watchers       map[string]*fsnotify.Watcher
	watchersMu     sync.RWMutex
	requests       []platformFilesCommon.IRecursiveWatchRequest
	requestsMu     sync.RWMutex
}

// NewParcelWatcher creates a new ParcelWatcher instance
func NewParcelWatcher() *ParcelWatcher {
	pw := &ParcelWatcher{
		Disposable:             baseCommon.NewDisposable(),
		onDidChangeFileEmitter: baseCommon.NewEmitter[[]platformFilesCommon.IFileChange](),
		onDidErrorEmitter:      baseCommon.NewEmitter[platformFilesCommon.IWatcherErrorEvent](),
		onDidLogMessageEmitter: baseCommon.NewEmitter[platformFilesCommon.ILogMessage](),
		watchers:               make(map[string]*fsnotify.Watcher),
		requests:               make([]platformFilesCommon.IRecursiveWatchRequest, 0),
	}

	pw.Register(pw.onDidChangeFileEmitter)
	pw.Register(pw.onDidErrorEmitter)
	pw.Register(pw.onDidLogMessageEmitter)

	return pw
}

// OnDidChangeFile returns the file change event
func (pw *ParcelWatcher) OnDidChangeFile() baseCommon.Event[[]platformFilesCommon.IFileChange] {
	return pw.onDidChangeFileEmitter.Event()
}

// OnDidError returns the error event
func (pw *ParcelWatcher) OnDidError() baseCommon.Event[platformFilesCommon.IWatcherErrorEvent] {
	return pw.onDidErrorEmitter.Event()
}

// OnDidLogMessage returns the log message event
func (pw *ParcelWatcher) OnDidLogMessage() baseCommon.Event[platformFilesCommon.ILogMessage] {
	return pw.onDidLogMessageEmitter.Event()
}

// Watch starts watching the provided recursive requests
func (pw *ParcelWatcher) Watch(requests []platformFilesCommon.IRecursiveWatchRequest) error {
	pw.requestsMu.Lock()
	defer pw.requestsMu.Unlock()

	// Stop existing watchers
	pw.stopAllWatchers()

	// Store new requests
	pw.requests = make([]platformFilesCommon.IRecursiveWatchRequest, len(requests))
	copy(pw.requests, requests)

	// Start new watchers
	for _, request := range requests {
		if err := pw.startWatcher(request); err != nil {
			pw.onDidErrorEmitter.Fire(platformFilesCommon.IWatcherErrorEvent{
				Error:   err.Error(),
				Request: request,
			})
		}
	}

	return nil
}

// SetVerboseLogging enables or disables verbose logging
func (pw *ParcelWatcher) SetVerboseLogging(enabled bool) error {
	pw.verboseLogging = enabled
	if enabled {
		pw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: "[ParcelWatcher] Verbose logging enabled",
		})
	}
	return nil
}

// Stop stops all watchers
func (pw *ParcelWatcher) Stop() error {
	pw.requestsMu.Lock()
	defer pw.requestsMu.Unlock()

	pw.stopAllWatchers()
	pw.Dispose()
	return nil
}

// startWatcher starts watching a single recursive request
func (pw *ParcelWatcher) startWatcher(request platformFilesCommon.IRecursiveWatchRequest) error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return err
	}

	pw.watchersMu.Lock()
	pw.watchers[request.GetPath()] = watcher
	pw.watchersMu.Unlock()

	// Add the root path
	if err := watcher.Add(request.GetPath()); err != nil {
		watcher.Close()
		return err
	}

	// Add all subdirectories for recursive watching
	if err := pw.addRecursivePaths(watcher, request); err != nil {
		watcher.Close()
		return err
	}

	// Start watching goroutine
	go pw.watchEvents(watcher, request)

	if pw.verboseLogging {
		pw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: "[ParcelWatcher] Started watching: " + request.GetPath(),
		})
	}

	return nil
}

// addRecursivePaths adds all subdirectories to the watcher
func (pw *ParcelWatcher) addRecursivePaths(watcher *fsnotify.Watcher, request platformFilesCommon.IRecursiveWatchRequest) error {
	return filepath.Walk(request.GetPath(), func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // Continue walking even if we hit an error
		}

		if info.IsDir() && path != request.GetPath() {
			// Check if path should be excluded
			if pw.shouldExcludePath(path, request) {
				return filepath.SkipDir
			}

			// Add directory to watcher
			if addErr := watcher.Add(path); addErr != nil && pw.verboseLogging {
				pw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
					Type:    "trace",
					Message: "[ParcelWatcher] Failed to add recursive path: " + path + " - " + addErr.Error(),
				})
			}
		}
		return nil
	})
}

// shouldExcludePath checks if a path should be excluded based on request filters
func (pw *ParcelWatcher) shouldExcludePath(path string, request platformFilesCommon.IRecursiveWatchRequest) bool {
	excludes := request.GetExcludes()
	if len(excludes) == 0 {
		return false
	}

	for _, exclude := range excludes {
		// Simple pattern matching - in a real implementation would use proper glob matching
		if matched, _ := filepath.Match(exclude, filepath.Base(path)); matched {
			return true
		}
		// Also check if the exclude pattern is contained in the path
		if strings.Contains(path, exclude) {
			return true
		}
	}
	return false
}

// watchEvents handles file system events for a watcher
func (pw *ParcelWatcher) watchEvents(watcher *fsnotify.Watcher, request platformFilesCommon.IRecursiveWatchRequest) {
	defer watcher.Close()

	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}
			pw.handleEvent(event, request)

		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}
			pw.onDidErrorEmitter.Fire(platformFilesCommon.IWatcherErrorEvent{
				Error:   err.Error(),
				Request: request,
			})
		}
	}
}

// handleEvent converts fsnotify events to platform file changes
func (pw *ParcelWatcher) handleEvent(event fsnotify.Event, request platformFilesCommon.IRecursiveWatchRequest) {
	// Convert fsnotify event to file change type
	var changeType platformFilesCommon.FileChangeType
	switch {
	case event.Op&fsnotify.Create == fsnotify.Create:
		changeType = platformFilesCommon.FileChangeTypeADDED
	case event.Op&fsnotify.Write == fsnotify.Write:
		changeType = platformFilesCommon.FileChangeTypeUPDATED
	case event.Op&fsnotify.Remove == fsnotify.Remove:
		changeType = platformFilesCommon.FileChangeTypeDELETED
	case event.Op&fsnotify.Rename == fsnotify.Rename:
		changeType = platformFilesCommon.FileChangeTypeDELETED // Treat rename as delete for simplicity
	default:
		return // Ignore other events
	}

	// Create file change
	resource := baseCommon.NewURI("file", "", event.Name, "", "")
	fileChange := platformFilesCommon.IFileChange{
		Type:     changeType,
		Resource: resource,
		CId:      request.GetCorrelationId(),
	}

	// Emit the change
	pw.onDidChangeFileEmitter.Fire([]platformFilesCommon.IFileChange{fileChange})

	if pw.verboseLogging {
		pw.onDidLogMessageEmitter.Fire(platformFilesCommon.ILogMessage{
			Type:    "trace",
			Message: "[ParcelWatcher] File change: " + event.Name + " (" + changeType.String() + ")",
		})
	}
}

// stopAllWatchers stops and removes all active watchers
func (pw *ParcelWatcher) stopAllWatchers() {
	pw.watchersMu.Lock()
	defer pw.watchersMu.Unlock()

	for path, watcher := range pw.watchers {
		watcher.Close()
		delete(pw.watchers, path)
	}
}

// Subscribe implements IRecursiveWatcherWithSubscribe interface
func (pw *ParcelWatcher) Subscribe(path string, callback func(error bool, change *platformFilesCommon.IFileChange)) baseCommon.IDisposable {
	// For simplicity, this implementation just subscribes to all file changes
	// and filters them in the callback based on the path
	return pw.OnDidChangeFile().Subscribe(func(changes []platformFilesCommon.IFileChange) {
		for _, change := range changes {
			if pw.pathMatches(change.Resource.GetPath(), path) {
				callback(false, &change)
			}
		}
	})
}

// pathMatches checks if the given path matches the subscribed path
func (pw *ParcelWatcher) pathMatches(changePath, subscribedPath string) bool {
	// Simple path matching - check if the change path starts with the subscribed path
	return strings.HasPrefix(changePath, subscribedPath)
}

// GetRequests returns a copy of the current requests (for NodeJS watcher optimization)
func (pw *ParcelWatcher) GetRequests() []platformFilesCommon.IRecursiveWatchRequest {
	pw.requestsMu.RLock()
	defer pw.requestsMu.RUnlock()

	result := make([]platformFilesCommon.IRecursiveWatchRequest, len(pw.requests))
	copy(result, pw.requests)
	return result
}
