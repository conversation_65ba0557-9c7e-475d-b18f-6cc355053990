/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"io"
	"os"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// readFileIntoStream reads a file into a writeable stream (matches TypeScript helper)
func readFileIntoStream(
	fd *os.File,
	stream *baseCommon.WriteableBufferStream,
	chunkSize int,
	totalBytesToRead int64,
) error {
	defer fd.Close()

	if chunkSize <= 0 {
		chunkSize = 64 * 1024 // 64KB default
	}

	buffer := make([]byte, chunkSize)
	var totalRead int64

	for {
		if totalBytesToRead > 0 && totalRead >= totalBytesToRead {
			break
		}

		bytesToRead := chunkSize
		if totalBytesToRead > 0 {
			remaining := totalBytesToRead - totalRead
			if remaining < int64(bytesToRead) {
				bytesToRead = int(remaining)
			}
		}

		n, err := fd.Read(buffer[:bytesToRead])
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}

		if n > 0 {
			chunk := baseCommon.VSBufferWrap(buffer[:n])
			if !stream.Write(chunk) {
				// Stream was closed
				break
			}
			totalRead += int64(n)
		}
	}

	stream.End(nil)
	return nil
}
