/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"sync"
	
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// EventHandler represents a function that handles events
type EventHandler[T any] func(T)

// Event represents an event that can be subscribed to
type Event[T any] interface {
	Subscribe(handler EventHandler[T]) IDisposable
}

// IDisposable represents something that can be disposed
type IDisposable interface {
	Dispose()
}

// Emitter implements Event and provides event emission capabilities
type Emitter[T any] struct {
	listeners map[uint64]EventHandler[T]
	mu        sync.RWMutex
	nextID    uint64
}

// NewEmitter creates a new emitter
func NewEmitter[T any]() *Emitter[T] {
	return &Emitter[T]{
		listeners: make(map[uint64]EventHandler[T]),
	}
}

// Subscribe adds a listener to the event
func (e *Emitter[T]) Subscribe(handler EventHandler[T]) IDisposable {
	if handler == nil {
		return &noneDisposable{}
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	e.nextID++
	id := e.nextID
	e.listeners[id] = handler

	return &disposableFunc{
		fn: func() {
			e.removeListener(id)
		},
	}
}

func (e *Emitter[T]) removeListener(id uint64) {
	e.mu.Lock()
	defer e.mu.Unlock()
	delete(e.listeners, id)
}

// Fire emits an event to all listeners
func (e *Emitter[T]) Fire(event T) {
	e.mu.RLock()
	listeners := make([]EventHandler[T], 0, len(e.listeners))
	for _, listener := range e.listeners {
		listeners = append(listeners, listener)
	}
	e.mu.RUnlock()

	for _, listener := range listeners {
		listener(event)
	}
}

// Event returns the event interface for this emitter
func (e *Emitter[T]) Event() Event[T] {
	return e
}

// Dispose disposes the emitter
func (e *Emitter[T]) Dispose() {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.listeners = make(map[uint64]EventHandler[T])
}

// Disposable base class for disposable objects
type Disposable struct {
	mu         sync.Mutex
	isDisposed bool
	toDispose  []IDisposable
}

// NewDisposable creates a new Disposable
func NewDisposable() *Disposable {
	return &Disposable{}
}

// Dispose disposes the object
func (d *Disposable) Dispose() {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.isDisposed {
		return
	}

	d.isDisposed = true
	for _, disposable := range d.toDispose {
		disposable.Dispose()
	}
	d.toDispose = nil
}

// Register registers a disposable to be disposed when this object is disposed
func (d *Disposable) Register(disposable IDisposable) IDisposable {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.isDisposed {
		disposable.Dispose()
	} else {
		d.toDispose = append(d.toDispose, disposable)
	}

	return disposable
}

// noneDisposable is a disposable that does nothing
type noneDisposable struct{}

func (n *noneDisposable) Dispose() {}

// disposableFunc wraps a function as a disposable
type disposableFunc struct {
	fn func()
}

func (d *disposableFunc) Dispose() {
	if d.fn != nil {
		d.fn()
		d.fn = nil
	}
}

// ISharedProcessLifecycleService is the service identifier for SharedProcessLifecycleService
var ISharedProcessLifecycleService = instantiationcommon.CreateDecorator[ISharedProcessLifecycleServiceInterface]("sharedProcessLifecycleService")

// ISharedProcessLifecycleServiceInterface defines the interface for the shared process lifecycle service
type ISharedProcessLifecycleServiceInterface interface {
	// ServiceBrand marker for dependency injection
	ServiceBrand() interface{}

	// OnWillShutdown is an event for when the application will shutdown
	OnWillShutdown() Event[struct{}]
}

// SharedProcessLifecycleService implements ISharedProcessLifecycleServiceInterface
type SharedProcessLifecycleService struct {
	*Disposable
	
	onWillShutdown *Emitter[struct{}]
	logService     logcommon.ILogService
}

// NewSharedProcessLifecycleService creates a new SharedProcessLifecycleService
func NewSharedProcessLifecycleService(logService logcommon.ILogService) *SharedProcessLifecycleService {
	s := &SharedProcessLifecycleService{
		Disposable:     NewDisposable(),
		onWillShutdown: NewEmitter[struct{}](),
		logService:     logService,
	}
	
	// Register the emitter for disposal
	s.Register(s.onWillShutdown)
	
	return s
}

// ServiceBrand implements the service brand marker for dependency injection
func (s *SharedProcessLifecycleService) ServiceBrand() interface{} {
	return nil
}

// OnWillShutdown returns the event for when the application will shutdown
func (s *SharedProcessLifecycleService) OnWillShutdown() Event[struct{}] {
	return s.onWillShutdown.Event()
}

// FireOnWillShutdown fires the onWillShutdown event
func (s *SharedProcessLifecycleService) FireOnWillShutdown() {
	s.logService.Trace("Lifecycle#onWillShutdown.fire()")
	s.onWillShutdown.Fire(struct{}{})
}