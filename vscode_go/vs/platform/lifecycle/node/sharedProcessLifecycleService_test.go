/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"testing"
	"time"
	
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

func TestSharedProcessLifecycleService(t *testing.T) {
	// Create a mock log service
	logService := logcommon.NewNullLogService()
	
	// Create the service
	service := NewSharedProcessLifecycleService(logService)
	defer service.Dispose()
	
	// Test that the service implements the interface
	var _ ISharedProcessLifecycleServiceInterface = service
	
	// Test event subscription
	eventFired := false
	disposable := service.OnWillShutdown().Subscribe(func(struct{}) {
		eventFired = true
	})
	defer disposable.Dispose()
	
	// Fire the event
	service.FireOnWillShutdown()
	
	// Give a small delay for event processing
	time.Sleep(1 * time.Millisecond)
	
	// Verify the event was fired
	if !eventFired {
		t.Error("Expected onWillShutdown event to be fired")
	}
}

func TestSharedProcessLifecycleServiceMultipleListeners(t *testing.T) {
	logService := logcommon.NewNullLogService()
	service := NewSharedProcessLifecycleService(logService)
	defer service.Dispose()
	
	// Subscribe multiple listeners
	count := 0
	
	disposable1 := service.OnWillShutdown().Subscribe(func(struct{}) {
		count++
	})
	defer disposable1.Dispose()
	
	disposable2 := service.OnWillShutdown().Subscribe(func(struct{}) {
		count++
	})
	defer disposable2.Dispose()
	
	// Fire the event
	service.FireOnWillShutdown()
	
	// Give a small delay for event processing
	time.Sleep(1 * time.Millisecond)
	
	// Verify both listeners were called
	if count != 2 {
		t.Errorf("Expected 2 listeners to be called, got %d", count)
	}
}

func TestSharedProcessLifecycleServiceDisposal(t *testing.T) {
	logService := logcommon.NewNullLogService()
	service := NewSharedProcessLifecycleService(logService)
	
	// Subscribe to the event
	eventFired := false
	disposable := service.OnWillShutdown().Subscribe(func(struct{}) {
		eventFired = true
	})
	
	// Dispose the service
	service.Dispose()
	
	// Try to fire the event after disposal
	service.FireOnWillShutdown()
	
	// Give a small delay
	time.Sleep(1 * time.Millisecond)
	
	// Event should not fire after disposal
	if eventFired {
		t.Error("Event should not fire after service disposal")
	}
	
	// Clean up
	disposable.Dispose()
}