/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"os"
	"path/filepath"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
)

// NativePaths represents the native paths structure
type NativePaths struct {
	HomeDir     string
	TmpDir      string
	UserDataDir string
}

// NativeEnvironmentService implements INativeEnvironmentService
type NativeEnvironmentService struct {
	args           environmentcommon.NativeParsedArgs
	paths          *NativePaths
	productService productcommon.IProductService

	// Memoized values
	appRoot                *string
	appRootMux             sync.Once
	userHome               *basecommon.URI
	userHomeMux            sync.Once
	userDataPath           *string
	userDataPathMux        sync.Once
	appSettingsHome        *basecommon.URI
	appSettingsHomeMux     sync.Once
	tmpDir                 *basecommon.URI
	tmpDirMux              sync.Once
	cacheHome              *basecommon.URI
	cacheHomeMux           sync.Once
	stateResource          *basecommon.URI
	stateResourceMux       sync.Once
	userRoamingDataHome    *basecommon.URI
	userRoamingDataHomeMux sync.Once
	userDataSyncHome       *basecommon.URI
	userDataSyncHomeMux    sync.Once
	logsHome               *basecommon.URI
	logsHomeMux            sync.Once
}

// NewNativeEnvironmentService creates a new NativeEnvironmentService
func NewNativeEnvironmentService(args environmentcommon.NativeParsedArgs, productService productcommon.IProductService) *NativeEnvironmentService {
	// Get user data path
	userDataDir, err := GetUserDataPath(productService.GetNameShort())
	if err != nil {
		panic(err)
	}

	paths := &NativePaths{
		HomeDir:     getUserHomeDir(),
		TmpDir:      os.TempDir(),
		UserDataDir: userDataDir,
	}

	return &NativeEnvironmentService{
		args:           args,
		paths:          paths,
		productService: productService,
	}
}

// getUserHomeDir gets the user home directory
func getUserHomeDir() string {
	if homeDir, err := os.UserHomeDir(); err == nil {
		return homeDir
	}
	return ""
}

// GetServiceBrand returns the service brand
func (n *NativeEnvironmentService) GetServiceBrand() interface{} {
	return nil
}

// GetArgs returns the parsed arguments
func (n *NativeEnvironmentService) GetArgs() environmentcommon.NativeParsedArgs {
	return n.args
}

// Args returns the parsed arguments (interface compliance)
// Equivalent to args property in TypeScript
func (n *NativeEnvironmentService) Args() environmentcommon.NativeParsedArgs {
	return n.GetArgs()
}

// UserHome returns the user home URI (interface compliance)
func (n *NativeEnvironmentService) UserHome() *basecommon.URI {
	return n.GetUserHome()
}

// TmpDir returns the temporary directory URI (interface compliance)
func (n *NativeEnvironmentService) TmpDir() *basecommon.URI {
	return n.GetTmpDir()
}

// ArgvResource returns the argv resource URI (interface compliance)
func (n *NativeEnvironmentService) ArgvResource() *basecommon.URI {
	return n.GetArgvResource()
}

// UserRoamingDataHome returns the user roaming data home URI (interface compliance)
func (n *NativeEnvironmentService) UserRoamingDataHome() *basecommon.URI {
	return n.GetUserRoamingDataHome()
}

// UserDataSyncHome returns the user data sync home URI (interface compliance)
func (n *NativeEnvironmentService) UserDataSyncHome() *basecommon.URI {
	return n.GetUserDataSyncHome()
}

// StateResource returns the state resource URI (interface compliance)
func (n *NativeEnvironmentService) StateResource() *basecommon.URI {
	return n.GetStateResource()
}

// UserDataPath returns the user data path (interface compliance)
func (n *NativeEnvironmentService) UserDataPath() string {
	return n.GetUserDataPath()
}

// MachineSettingsResource returns the machine settings resource URI (interface compliance)
func (n *NativeEnvironmentService) MachineSettingsResource() *basecommon.URI {
	return n.GetMachineSettingsResource()
}

// LogsHome returns the logs home URI (interface compliance)
func (n *NativeEnvironmentService) LogsHome() *basecommon.URI {
	return n.GetLogsHome()
}

// GetAppRoot returns the application root path (memoized)
func (n *NativeEnvironmentService) GetAppRoot() string {
	n.appRootMux.Do(func() {
		// Get the directory of the current executable
		if execPath, err := os.Executable(); err == nil {
			appRoot := filepath.Dir(execPath)
			n.appRoot = &appRoot
		} else {
			// Fallback to current working directory
			if cwd, err := os.Getwd(); err == nil {
				n.appRoot = &cwd
			} else {
				defaultRoot := "."
				n.appRoot = &defaultRoot
			}
		}
	})
	return *n.appRoot
}

// AppRoot returns the application root path (interface compliance)
// Equivalent to appRoot property in TypeScript
func (n *NativeEnvironmentService) AppRoot() string {
	return n.GetAppRoot()
}

// AppSettingsHome returns the app settings home URI (interface compliance)
// Equivalent to appSettingsHome property in TypeScript
func (n *NativeEnvironmentService) AppSettingsHome() *basecommon.URI {
	return n.GetAppSettingsHome()
}

// GetUserHome returns the user home URI (memoized)
func (n *NativeEnvironmentService) GetUserHome() *basecommon.URI {
	n.userHomeMux.Do(func() {
		uri := basecommon.FileURI(n.paths.HomeDir)
		n.userHome = uri
	})
	return n.userHome
}

// GetUserDataPath returns the user data path (memoized)
func (n *NativeEnvironmentService) GetUserDataPath() string {
	n.userDataPathMux.Do(func() {
		n.userDataPath = &n.paths.UserDataDir
	})
	return *n.userDataPath
}

// GetAppSettingsHome returns the app settings home URI (memoized)
func (n *NativeEnvironmentService) GetAppSettingsHome() *basecommon.URI {
	n.appSettingsHomeMux.Do(func() {
		settingsPath := filepath.Join(n.GetUserDataPath(), "User")
		uri := basecommon.FileURI(settingsPath)
		n.appSettingsHome = uri
	})
	return n.appSettingsHome
}

// GetTmpDir returns the temporary directory URI (memoized)
func (n *NativeEnvironmentService) GetTmpDir() *basecommon.URI {
	n.tmpDirMux.Do(func() {
		uri := basecommon.FileURI(n.paths.TmpDir)
		n.tmpDir = uri
	})
	return n.tmpDir
}

// GetCacheHome returns the cache home URI (memoized)
func (n *NativeEnvironmentService) GetCacheHome() *basecommon.URI {
	n.cacheHomeMux.Do(func() {
		uri := basecommon.FileURI(n.GetUserDataPath())
		n.cacheHome = uri
	})
	return n.cacheHome
}

// GetStateResource returns the state resource URI (memoized)
func (n *NativeEnvironmentService) GetStateResource() *basecommon.URI {
	n.stateResourceMux.Do(func() {
		statePath := filepath.Join(n.GetAppSettingsHome().GetFSPath(), "globalStorage", "storage.json")
		uri := basecommon.FileURI(statePath)
		n.stateResource = uri
	})
	return n.stateResource
}

// GetUserRoamingDataHome returns the user roaming data home URI (memoized)
func (n *NativeEnvironmentService) GetUserRoamingDataHome() *basecommon.URI {
	n.userRoamingDataHomeMux.Do(func() {
		// For now, use the same as app settings home
		// In the full implementation, this would use vscodeUserData scheme
		n.userRoamingDataHome = n.GetAppSettingsHome()
	})
	return n.userRoamingDataHome
}

// GetUserDataSyncHome returns the user data sync home URI (memoized)
func (n *NativeEnvironmentService) GetUserDataSyncHome() *basecommon.URI {
	n.userDataSyncHomeMux.Do(func() {
		syncPath := filepath.Join(n.GetAppSettingsHome().GetFSPath(), "sync")
		uri := basecommon.FileURI(syncPath)
		n.userDataSyncHome = uri
	})
	return n.userDataSyncHome
}

// GetLogsHome returns the logs home URI (memoized)
func (n *NativeEnvironmentService) GetLogsHome() *basecommon.URI {
	n.logsHomeMux.Do(func() {
		// Check if logs path is specified in args
		var logsPath string
		if argsLogsPath, exists := n.args["logsPath"]; exists && argsLogsPath != nil {
			if pathStr, ok := argsLogsPath.(string); ok {
				logsPath = pathStr
			}
		}

		if logsPath == "" {
			// Generate a timestamp-based key
			key := basecommon.ToLocalISOString()
			logsPath = filepath.Join(n.GetUserDataPath(), "logs", key)
		}

		uri := basecommon.FileURI(logsPath)
		n.logsHome = uri
	})
	return n.logsHome
}

// GetProductService returns the product service
func (n *NativeEnvironmentService) GetProductService() productcommon.IProductService {
	return n.productService
}

// Implement remaining methods from INativeEnvironmentService interface
// For brevity, implementing basic versions

func (n *NativeEnvironmentService) GetExtensionsPath() string {
	return filepath.Join(n.GetUserDataPath(), "extensions")
}

func (n *NativeEnvironmentService) GetExtensionsDownloadLocation() *basecommon.URI {
	path := filepath.Join(n.GetUserDataPath(), "CachedExtensions")
	return basecommon.FileURI(path)
}

func (n *NativeEnvironmentService) GetBuiltinExtensionsPath() string {
	return filepath.Join(n.GetAppRoot(), "extensions")
}

func (n *NativeEnvironmentService) GetMachineSettingsResource() *basecommon.URI {
	path := filepath.Join(n.GetAppSettingsHome().GetFSPath(), "settings.json")
	return basecommon.FileURI(path)
}

func (n *NativeEnvironmentService) GetUseInMemorySecretStorage() *bool {
	if val, exists := n.args["use-inmemory-secretstorage"]; exists {
		if boolVal, ok := val.(bool); ok {
			return &boolVal
		}
	}
	return nil
}

func (n *NativeEnvironmentService) GetCrossOriginIsolated() *bool {
	if val, exists := n.args["enable-coi"]; exists {
		if boolVal, ok := val.(bool); ok {
			return &boolVal
		}
	}
	return nil
}

// Add other required methods with basic implementations
func (n *NativeEnvironmentService) GetIsExtensionDevelopment() bool                       { return false }
func (n *NativeEnvironmentService) GetDisableExtensions() bool                            { return false }
func (n *NativeEnvironmentService) GetDisableDefaultExtensions() bool                     { return false }
func (n *NativeEnvironmentService) GetVerbose() bool                                      { return false }
func (n *NativeEnvironmentService) GetLogLevel() *string                                  { return nil }
func (n *NativeEnvironmentService) GetExtensionDevelopmentLocationURI() []*basecommon.URI { return nil }
func (n *NativeEnvironmentService) GetExtensionDevelopmentKind() []environmentcommon.ExtensionKind {
	return nil
}
func (n *NativeEnvironmentService) GetExtensionTestsLocationURI() *basecommon.URI { return nil }
func (n *NativeEnvironmentService) GetRemoteAuthority() *string                   { return nil }
func (n *NativeEnvironmentService) GetExpectsResolverExtension() bool             { return false }
func (n *NativeEnvironmentService) GetDriverHandle() *string                      { return nil }
func (n *NativeEnvironmentService) GetDriverVerbose() bool                        { return false }
func (n *NativeEnvironmentService) GetWebviewExternalEndpoint() string            { return "" }
func (n *NativeEnvironmentService) GetWebviewResourceRoot() string                { return "" }
func (n *NativeEnvironmentService) GetWebviewCspSource() string                   { return "" }
func (n *NativeEnvironmentService) GetUserDataSyncLogResource() *basecommon.URI   { return nil }
func (n *NativeEnvironmentService) GetSyncExtensionsResource() *basecommon.URI    { return nil }
func (n *NativeEnvironmentService) GetSyncGlobalStateResource() *basecommon.URI   { return nil }
func (n *NativeEnvironmentService) GetSyncKeybindingsResource() *basecommon.URI   { return nil }
func (n *NativeEnvironmentService) GetSyncSettingsResource() *basecommon.URI      { return nil }
func (n *NativeEnvironmentService) GetSyncSnippetsResource() *basecommon.URI      { return nil }
func (n *NativeEnvironmentService) GetSyncTasksResource() *basecommon.URI         { return nil }
func (n *NativeEnvironmentService) GetTelemetryLogResource() *basecommon.URI      { return nil }
func (n *NativeEnvironmentService) GetPolicyFile() *basecommon.URI                { return nil }
func (n *NativeEnvironmentService) GetKeyboardLayoutResource() *basecommon.URI    { return nil }
func (n *NativeEnvironmentService) GetArgvResource() *basecommon.URI              { return nil }
func (n *NativeEnvironmentService) GetUntitledWorkspacesHome() *basecommon.URI    { return nil }
