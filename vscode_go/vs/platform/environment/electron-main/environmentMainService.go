/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"context"
	"os"
	"runtime"
	"strings"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	ipcnode "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/ipc/node"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	environmentnode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/node"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
)

// IEnvironmentMainService represents the main environment service interface for electron-main
type IEnvironmentMainService interface {
	environmentcommon.INativeEnvironmentService

	// --- backup paths
	BackupHome() string

	// --- V8 code caching
	CodeCachePath() *string
	UseCodeCache() bool

	// --- IPC
	MainIPCHandle() string
	MainLockfile() string

	// --- config
	DisableUpdates() bool

	// TODO@deepak1556 TODO@bpasero temporary until a real fix lands upstream
	EnableRDPDisplayTracking() bool

	UnsetSnapExportedVariables()
	RestoreSnapExportedVariables()
}

// EnvironmentMainService implements IEnvironmentMainService
type EnvironmentMainService struct {
	*environmentnode.NativeEnvironmentService
	snapEnv    map[string]string
	snapEnvMux sync.RWMutex

	// Memoized values
	backupHome                  *string
	backupHomeMux               sync.Once
	mainIPCHandle               *string
	mainIPCHandleMux            sync.Once
	mainLockfile                *string
	mainLockfileMux             sync.Once
	disableUpdates              *bool
	disableUpdatesMux           sync.Once
	crossOriginIsolated         *bool
	crossOriginIsolatedMux      sync.Once
	enableRDPDisplayTracking    *bool
	enableRDPDisplayTrackingMux sync.Once
	codeCachePath               *string
	codeCachePathMux            sync.Once
	useCodeCache                *bool
	useCodeCacheMux             sync.Once
}

// NewEnvironmentMainService creates a new EnvironmentMainService
func NewEnvironmentMainService(args environmentcommon.NativeParsedArgs, productService productcommon.IProductService) *EnvironmentMainService {
	nativeService := environmentnode.NewNativeEnvironmentService(args, productService)

	return &EnvironmentMainService{
		NativeEnvironmentService: nativeService,
		snapEnv:                  make(map[string]string),
	}
}

// GetBackupHome returns the backup home directory (memoized)
func (e *EnvironmentMainService) GetBackupHome() string {
	e.backupHomeMux.Do(func() {
		backupHome := basecommon.Join(e.GetUserDataPath(), "Backups")
		e.backupHome = &backupHome
	})
	return *e.backupHome
}

// BackupHome returns the backup home directory (interface compliance)
func (e *EnvironmentMainService) BackupHome() string {
	return e.GetBackupHome()
}

// CodeCachePath returns the code cache path (interface compliance)
func (e *EnvironmentMainService) CodeCachePath() *string {
	return e.GetCodeCachePath()
}

// UseCodeCache returns whether code cache is enabled (interface compliance)
func (e *EnvironmentMainService) UseCodeCache() bool {
	return e.GetUseCodeCache()
}

// MainIPCHandle returns the main IPC handle (interface compliance)
func (e *EnvironmentMainService) MainIPCHandle() string {
	return e.GetMainIPCHandle()
}

// MainLockfile returns the main lockfile path (interface compliance)
func (e *EnvironmentMainService) MainLockfile() string {
	return e.GetMainLockfile()
}

// DisableUpdates returns whether updates are disabled (interface compliance)
func (e *EnvironmentMainService) DisableUpdates() bool {
	return e.GetDisableUpdates()
}

// CrossOriginIsolated returns whether cross-origin isolation is enabled (interface compliance)
func (e *EnvironmentMainService) CrossOriginIsolated() *bool {
	enabled := e.GetCrossOriginIsolated()
	return &enabled
}

// EnableRDPDisplayTracking returns whether RDP display tracking is enabled (interface compliance)
func (e *EnvironmentMainService) EnableRDPDisplayTracking() bool {
	return e.GetEnableRDPDisplayTracking()
}

// GetMainIPCHandle returns the main IPC handle (memoized)
func (e *EnvironmentMainService) GetMainIPCHandle() string {
	e.mainIPCHandleMux.Do(func() {
		handle := ipcnode.CreateStaticIPCHandle(e.GetUserDataPath(), "main", e.GetProductService().GetVersion())
		e.mainIPCHandle = &handle
	})
	return *e.mainIPCHandle
}

// GetMainLockfile returns the main lockfile path (memoized)
func (e *EnvironmentMainService) GetMainLockfile() string {
	e.mainLockfileMux.Do(func() {
		lockfile := basecommon.Join(e.GetUserDataPath(), "code.lock")
		e.mainLockfile = &lockfile
	})
	return *e.mainLockfile
}

// GetDisableUpdates returns whether updates are disabled (memoized)
func (e *EnvironmentMainService) GetDisableUpdates() bool {
	e.disableUpdatesMux.Do(func() {
		args := e.GetArgs()
		disabled := args["disable-updates"] != nil && args["disable-updates"].(bool)
		e.disableUpdates = &disabled
	})
	return *e.disableUpdates
}

// GetCrossOriginIsolated returns whether cross-origin isolation is enabled (memoized)
func (e *EnvironmentMainService) GetCrossOriginIsolated() bool {
	e.crossOriginIsolatedMux.Do(func() {
		args := e.GetArgs()
		enabled := args["enable-coi"] != nil && args["enable-coi"].(bool)
		e.crossOriginIsolated = &enabled
	})
	return *e.crossOriginIsolated
}

// GetEnableRDPDisplayTracking returns whether RDP display tracking is enabled (memoized)
func (e *EnvironmentMainService) GetEnableRDPDisplayTracking() bool {
	e.enableRDPDisplayTrackingMux.Do(func() {
		args := e.GetArgs()
		enabled := args["enable-rdp-display-tracking"] != nil && args["enable-rdp-display-tracking"].(bool)
		e.enableRDPDisplayTracking = &enabled
	})
	return *e.enableRDPDisplayTracking
}

// GetCodeCachePath returns the code cache path (memoized)
func (e *EnvironmentMainService) GetCodeCachePath() *string {
	e.codeCachePathMux.Do(func() {
		if path := os.Getenv("VSCODE_CODE_CACHE_PATH"); path != "" {
			e.codeCachePath = &path
		} else {
			e.codeCachePath = nil
		}
	})
	return e.codeCachePath
}

// GetUseCodeCache returns whether code cache is enabled (memoized)
func (e *EnvironmentMainService) GetUseCodeCache() bool {
	e.useCodeCacheMux.Do(func() {
		useCache := e.GetCodeCachePath() != nil
		e.useCodeCache = &useCache
	})
	return *e.useCodeCache
}

// UnsetSnapExportedVariables unsets snap exported variables on Linux
func (e *EnvironmentMainService) UnsetSnapExportedVariables() {
	if runtime.GOOS != "linux" {
		return
	}

	e.snapEnvMux.Lock()
	defer e.snapEnvMux.Unlock()

	for _, envVar := range os.Environ() {
		// Parse environment variable in format "KEY=VALUE"
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) != 2 {
			continue
		}
		key := parts[0]

		if strings.HasSuffix(key, "_VSCODE_SNAP_ORIG") {
			originalKey := key[:len(key)-17] // Remove the _VSCODE_SNAP_ORIG suffix

			// Skip if we already have this key preserved
			if _, exists := e.snapEnv[originalKey]; exists {
				continue
			}

			// Preserve the original value in case the snap env is re-entered
			if originalValue := os.Getenv(originalKey); originalValue != "" {
				e.snapEnv[originalKey] = originalValue
			}

			// Copy the original value from before entering the snap env if available,
			// if not delete the env variable.
			if snapOrigValue := os.Getenv(key); snapOrigValue != "" {
				os.Setenv(originalKey, snapOrigValue)
			} else {
				os.Unsetenv(originalKey)
			}
		}
	}
}

// RestoreSnapExportedVariables restores snap exported variables on Linux
func (e *EnvironmentMainService) RestoreSnapExportedVariables() {
	if runtime.GOOS != "linux" {
		return
	}

	e.snapEnvMux.Lock()
	defer e.snapEnvMux.Unlock()

	for key, value := range e.snapEnv {
		os.Setenv(key, value)
		delete(e.snapEnv, key)
	}
}

// Service registration and dependency injection helpers

// EnvironmentMainServiceContext provides context for environment main service dependency injection
type EnvironmentMainServiceContext struct {
	context.Context
	environmentMainService IEnvironmentMainService
}

// NewEnvironmentMainServiceContext creates a new context with environment main service
func NewEnvironmentMainServiceContext(ctx context.Context, environmentMainService IEnvironmentMainService) *EnvironmentMainServiceContext {
	return &EnvironmentMainServiceContext{
		Context:                ctx,
		environmentMainService: environmentMainService,
	}
}

// GetEnvironmentMainService retrieves the environment main service from context
func GetEnvironmentMainService(ctx context.Context) IEnvironmentMainService {
	if esc, ok := ctx.(*EnvironmentMainServiceContext); ok {
		return esc.environmentMainService
	}
	// Return a default implementation or panic based on requirements
	panic("EnvironmentMainService not found in context")
}

// ServiceIdentifier for dependency injection
type EnvironmentMainServiceIdentifier struct{}

// GetServiceID returns the service identifier
func (e *EnvironmentMainServiceIdentifier) GetServiceID() string {
	return "environmentMainService"
}

// Global service identifier instance
var IEnvironmentMainServiceID = &EnvironmentMainServiceIdentifier{}
