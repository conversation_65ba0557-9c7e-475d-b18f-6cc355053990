/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	terminalcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/terminal/common"
)

// IExternalTerminalSettings represents external terminal settings
type IExternalTerminalSettings struct {
	LinuxExec   *string `json:"linuxExec,omitempty"`
	OsxExec     *string `json:"osxExec,omitempty"`
	WindowsExec *string `json:"windowsExec,omitempty"`
}

// ITerminalForPlatform represents terminal executables for different platforms
type ITerminalForPlatform struct {
	Windows string `json:"windows"`
	Linux   string `json:"linux"`
	Osx     string `json:"osx"`
}

// IExternalTerminalService represents the external terminal service interface
type IExternalTerminalService interface {
	// Service brand for dependency injection
	GetServiceBrand() interface{}

	// OpenTerminal opens a terminal with the given configuration
	OpenTerminal(configuration *IExternalTerminalSettings, cwd *string) error

	// RunInTerminal runs a command in a terminal
	RunInTerminal(title string, cwd string, args []string, env terminalcommon.IProcessEnvironment, settings *IExternalTerminalSettings) (*int, error)

	// GetDefaultTerminalForPlatforms returns the default terminal for each platform
	GetDefaultTerminalForPlatforms() (*ITerminalForPlatform, error)
}

// IExternalTerminalConfiguration represents external terminal configuration
type IExternalTerminalConfiguration struct {
	Terminal struct {
		ExplorerKind string                     `json:"explorerKind"` // 'integrated' | 'external' | 'both'
		External     *IExternalTerminalSettings `json:"external"`
	} `json:"terminal"`
}

// DEFAULT_TERMINAL_OSX is the default terminal application on macOS
const DEFAULT_TERMINAL_OSX = "Terminal.app"