/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"testing"

	externalTerminalCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/externalTerminal/common"
)

func TestExternalTerminalService_GetDefaultTerminalForPlatforms(t *testing.T) {
	service := &ExternalTerminalService{}
	platforms, err := service.GetDefaultTerminalForPlatforms()
	
	if err != nil {
		t.Fatalf("GetDefaultTerminalForPlatforms failed: %v", err)
	}
	
	if platforms == nil {
		t.Fatal("GetDefaultTerminalForPlatforms returned nil")
	}
	
	if platforms.Windows == "" {
		t.Error("Windows terminal should not be empty")
	}
	
	if platforms.Linux == "" {
		t.Error("Linux terminal should not be empty")
	}
	
	if platforms.Osx == "" {
		t.Error("OSX terminal should not be empty")
	}
}

func TestWindowsExternalTerminalService_GetDefaultTerminalWindows(t *testing.T) {
	service := &WindowsExternalTerminalService{}
	terminal := service.getDefaultTerminalWindows()
	
	if terminal == "" {
		t.Error("Default Windows terminal should not be empty")
	}
	
	// Should contain cmd.exe
	if !contains(terminal, "cmd.exe") {
		t.Errorf("Expected terminal to contain 'cmd.exe', got: %s", terminal)
	}
}

func TestLinuxExternalTerminalService_GetDefaultTerminalLinuxReady(t *testing.T) {
	service := &LinuxExternalTerminalService{}
	terminal, err := service.getDefaultTerminalLinuxReady()
	
	if err != nil {
		t.Fatalf("getDefaultTerminalLinuxReady failed: %v", err)
	}
	
	if terminal == "" {
		t.Error("Default Linux terminal should not be empty")
	}
}

func TestExternalTerminalSettings(t *testing.T) {
	settings := &externalTerminalCommon.IExternalTerminalSettings{
		WindowsExec: stringPtr("cmd.exe"),
		LinuxExec:   stringPtr("gnome-terminal"),
		OsxExec:     stringPtr("Terminal.app"),
	}
	
	if settings.WindowsExec == nil || *settings.WindowsExec != "cmd.exe" {
		t.Error("WindowsExec should be set to cmd.exe")
	}
	
	if settings.LinuxExec == nil || *settings.LinuxExec != "gnome-terminal" {
		t.Error("LinuxExec should be set to gnome-terminal")
	}
	
	if settings.OsxExec == nil || *settings.OsxExec != "Terminal.app" {
		t.Error("OsxExec should be set to Terminal.app")
	}
}

func TestTerminalForPlatform(t *testing.T) {
	platforms := &externalTerminalCommon.ITerminalForPlatform{
		Windows: "cmd.exe",
		Linux:   "gnome-terminal",
		Osx:     "Terminal.app",
	}
	
	if platforms.Windows != "cmd.exe" {
		t.Errorf("Expected Windows terminal to be 'cmd.exe', got: %s", platforms.Windows)
	}
	
	if platforms.Linux != "gnome-terminal" {
		t.Errorf("Expected Linux terminal to be 'gnome-terminal', got: %s", platforms.Linux)
	}
	
	if platforms.Osx != "Terminal.app" {
		t.Errorf("Expected OSX terminal to be 'Terminal.app', got: %s", platforms.Osx)
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr
}