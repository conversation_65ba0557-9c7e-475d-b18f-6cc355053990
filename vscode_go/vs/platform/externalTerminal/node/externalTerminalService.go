/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"

	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	basepfs "github.com/yudaprama/kawai-agent/vscode_go/vs/base/node"
	vs "github.com/yudaprama/kawai-agent/vscode_go/vs"
	externalTerminalCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/externalTerminal/common"
	terminalcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/terminal/common"
)

var TERMINAL_TITLE = vs.Localize("console.title", "VS Code Console")

// ExternalTerminalService is the abstract base class for external terminal services
type ExternalTerminalService struct {
	serviceBrand interface{}
}

// GetServiceBrand returns the service brand for dependency injection
func (e *ExternalTerminalService) GetServiceBrand() interface{} {
	return e.serviceBrand
}

// GetDefaultTerminalForPlatforms returns the default terminal for each platform
func (e *ExternalTerminalService) GetDefaultTerminalForPlatforms() (*externalTerminalCommon.ITerminalForPlatform, error) {
	linuxService := &LinuxExternalTerminalService{}
	linuxDefault, err := linuxService.getDefaultTerminalLinuxReady()
	if err != nil {
		return nil, err
	}

	windowsService := &WindowsExternalTerminalService{}
	return &externalTerminalCommon.ITerminalForPlatform{
		Windows: windowsService.getDefaultTerminalWindows(),
		Linux:   linuxDefault,
		Osx:     "xterm",
	}, nil
}

// WindowsExternalTerminalService implements external terminal service for Windows
type WindowsExternalTerminalService struct {
	ExternalTerminalService
}

const (
	CMD = "cmd.exe"
)

var (
	defaultTerminalWindows     string
	defaultTerminalWindowsOnce sync.Once
	wtExePath                  string
	wtExePathOnce              sync.Once
)

// OpenTerminal opens a terminal with the given configuration
func (w *WindowsExternalTerminalService) OpenTerminal(configuration *externalTerminalCommon.IExternalTerminalSettings, cwd *string) error {
	return w.spawnTerminal(configuration, basepfs.GetWindowsShell(mapToStringMap(os.Environ())), cwd)
}

// spawnTerminal spawns a terminal process
func (w *WindowsExternalTerminalService) spawnTerminal(configuration *externalTerminalCommon.IExternalTerminalSettings, command string, cwd *string) error {
	execPath := ""
	if configuration != nil && configuration.WindowsExec != nil {
		execPath = *configuration.WindowsExec
	} else {
		execPath = w.getDefaultTerminalWindows()
	}

	// Make the drive letter uppercase on Windows (see #9448)
	if cwd != nil && len(*cwd) > 1 && (*cwd)[1] == ':' {
		upper := strings.ToUpper(string((*cwd)[0]))
		*cwd = upper + (*cwd)[1:]
	}

	// cmder ignores the environment cwd and instead opts to always open in %USERPROFILE%
	// unless otherwise specified
	basename := strings.ToLower(common.Basename(execPath, ".exe"))
	if basename == "cmder" {
		var args []string
		if cwd != nil {
			args = []string{*cwd}
		}
		cmd := exec.Command(execPath, args...)
		return cmd.Start()
	}

	cmdArgs := []string{"/c", "start", "/wait"}
	if strings.Contains(execPath, " ") {
		// The "" argument is the window title. Without this, exec doesn't work when the path
		// contains spaces. #6590
		// Title is Execution Path. #220129
		cmdArgs = append(cmdArgs, execPath)
	}
	cmdArgs = append(cmdArgs, execPath)
	// Add starting directory parameter for Windows Terminal (see #90734)
	if basename == "wt" {
		cmdArgs = append(cmdArgs, "-d .")
	}

	env := getSanitizedEnvironment()
	cmd := exec.Command(command, cmdArgs...)
	cmd.Dir = ""
	if cwd != nil {
		cmd.Dir = *cwd
	}
	cmd.Env = mapToEnvSlice(env)

	return cmd.Start()
}

// RunInTerminal runs a command in a terminal
func (w *WindowsExternalTerminalService) RunInTerminal(title string, dir string, args []string, envVars terminalcommon.IProcessEnvironment, settings *externalTerminalCommon.IExternalTerminalSettings) (*int, error) {
	execPath := ""
	if settings != nil && settings.WindowsExec != nil {
		execPath = *settings.WindowsExec
	} else {
		execPath = w.getDefaultTerminalWindows()
	}

	wt, err := w.getWtExePath()
	if err != nil {
		return nil, err
	}

	terminalTitle := fmt.Sprintf("\"%s - %s\"", dir, TERMINAL_TITLE)
	command := fmt.Sprintf("\"%s\" & pause", strings.Join(args, "\" \""))

	// merge environment variables into a copy of the process.env
	env := getSanitizedEnvironment()
	for key, value := range envVars {
		env[key] = value
	}

	// delete environment variables that have a null value
	for key, value := range env {
		if value == "" {
			delete(env, key)
		}
	}

	var spawnExec string
	var cmdArgs []string

	if common.Basename(execPath, ".exe") == "wt" {
		// Handle Windows Terminal specially; -d to set the cwd and run a cmd.exe instance
		// inside it
		spawnExec = execPath
		cmdArgs = []string{"-d", ".", CMD, "/c", command}
	} else if wt != "" {
		// prefer to use the window terminal to spawn if it's available instead
		// of start, since that allows ctrl+c handling (#81322)
		spawnExec = wt
		cmdArgs = []string{"-d", ".", execPath, "/c", command}
	} else {
		spawnExec = CMD
		cmdArgs = []string{"/c", "start", terminalTitle, "/wait", execPath, "/c", fmt.Sprintf("\"%s\"", command)}
	}

	cmd := exec.Command(spawnExec, cmdArgs...)
	cmd.Dir = dir
	cmd.Env = mapToEnvSlice(env)

	err = cmd.Start()
	if err != nil {
		return nil, improveError(err)
	}

	return nil, nil
}

// getDefaultTerminalWindows returns the default Windows terminal
func (w *WindowsExternalTerminalService) getDefaultTerminalWindows() string {
	defaultTerminalWindowsOnce.Do(func() {
		windir := os.Getenv("windir")
		if windir == "" {
			windir = "C:\\Windows"
		}

		isWoW64 := os.Getenv("PROCESSOR_ARCHITEW6432") != ""
		systemDir := "System32"
		if isWoW64 {
			systemDir = "Sysnative"
		}

		defaultTerminalWindows = filepath.Join(windir, systemDir, "cmd.exe")
	})
	return defaultTerminalWindows
}

// getWtExePath returns the Windows Terminal executable path
func (w *WindowsExternalTerminalService) getWtExePath() (string, error) {
	wtExePathOnce.Do(func() {
		path, err := basepfs.FindExecutable("wt", "", nil, mapToStringMap(os.Environ()), nil)
		if err == nil {
			wtExePath = path
		}
	})
	return wtExePath, nil
}

// MacExternalTerminalService implements external terminal service for macOS
type MacExternalTerminalService struct {
	ExternalTerminalService
}

const OSASCRIPT = "/usr/bin/osascript" // osascript is the AppleScript interpreter on OS X

// OpenTerminal opens a terminal with the given configuration
func (m *MacExternalTerminalService) OpenTerminal(configuration *externalTerminalCommon.IExternalTerminalSettings, cwd *string) error {
	return m.spawnTerminal(configuration, cwd)
}

// RunInTerminal runs a command in a terminal
func (m *MacExternalTerminalService) RunInTerminal(title string, dir string, args []string, envVars terminalcommon.IProcessEnvironment, settings *externalTerminalCommon.IExternalTerminalSettings) (*int, error) {
	terminalApp := externalTerminalCommon.DEFAULT_TERMINAL_OSX
	if settings != nil && settings.OsxExec != nil {
		terminalApp = *settings.OsxExec
	}

	if terminalApp == externalTerminalCommon.DEFAULT_TERMINAL_OSX || terminalApp == "iTerm.app" {
		// On OS X we launch an AppleScript that creates (or reuses) a Terminal window
		// and then launches the program inside that window.

		script := "TerminalHelper"
		if terminalApp != externalTerminalCommon.DEFAULT_TERMINAL_OSX {
			script = "iTermHelper"
		}

		scriptpath := common.FileAccess.AsFileUri(fmt.Sprintf("vs/workbench/contrib/externalTerminal/node/%s.scpt", script)).FSPath()

		osaArgs := []string{
			scriptpath,
			"-t", title,
			"-w", dir,
		}

		if title == "" {
			osaArgs[2] = TERMINAL_TITLE
		}

		for _, a := range args {
			osaArgs = append(osaArgs, "-a", a)
		}

		if envVars != nil {
			// merge environment variables into a copy of the process.env
			env := getSanitizedEnvironment()
			for key, value := range envVars {
				env[key] = value
			}

			for key, value := range env {
				if value == "" {
					osaArgs = append(osaArgs, "-u", key)
				} else {
					osaArgs = append(osaArgs, "-e", fmt.Sprintf("%s=%s", key, value))
				}
			}
		}

		cmd := exec.Command(OSASCRIPT, osaArgs...)
		output, err := cmd.CombinedOutput()
		if err != nil {
			if len(output) > 0 {
				lines := strings.Split(string(output), "\n")
				if len(lines) > 0 {
					return nil, fmt.Errorf("%s", lines[0])
				}
			}
			return nil, fmt.Errorf("Script '%s' failed with exit code %d", script, cmd.ProcessState.ExitCode())
		}
	} else {
		return nil, fmt.Errorf("'%s' not supported", terminalApp)
	}

	return nil, nil
}

// spawnTerminal spawns a terminal process on macOS
func (m *MacExternalTerminalService) spawnTerminal(configuration *externalTerminalCommon.IExternalTerminalSettings, cwd *string) error {
	terminalApp := externalTerminalCommon.DEFAULT_TERMINAL_OSX
	if configuration != nil && configuration.OsxExec != nil {
		terminalApp = *configuration.OsxExec
	}

	args := []string{"-a", terminalApp}
	if cwd != nil {
		args = append(args, *cwd)
	}

	env := getSanitizedEnvironment()
	cmd := exec.Command("/usr/bin/open", args...)
	cmd.Dir = ""
	if cwd != nil {
		cmd.Dir = *cwd
	}
	cmd.Env = mapToEnvSlice(env)

	return cmd.Start()
}

// LinuxExternalTerminalService implements external terminal service for Linux
type LinuxExternalTerminalService struct {
	ExternalTerminalService
}

var (
	WAIT_MESSAGE                   = vs.Localize("press.any.key", "Press any key to continue...")
	defaultTerminalLinuxReady      string
	defaultTerminalLinuxReadyOnce  sync.Once
	defaultTerminalLinuxReadyError error
)

// OpenTerminal opens a terminal with the given configuration
func (l *LinuxExternalTerminalService) OpenTerminal(configuration *externalTerminalCommon.IExternalTerminalSettings, cwd *string) error {
	return l.spawnTerminal(configuration, cwd)
}

// RunInTerminal runs a command in a terminal
func (l *LinuxExternalTerminalService) RunInTerminal(title string, dir string, args []string, envVars terminalcommon.IProcessEnvironment, settings *externalTerminalCommon.IExternalTerminalSettings) (*int, error) {
	var execPath string
	var err error

	if settings != nil && settings.LinuxExec != nil {
		execPath = *settings.LinuxExec
	} else {
		execPath, err = l.getDefaultTerminalLinuxReady()
		if err != nil {
			return nil, err
		}
	}

	termArgs := []string{}
	if strings.Contains(execPath, "gnome-terminal") {
		termArgs = append(termArgs, "-x")
	} else {
		termArgs = append(termArgs, "-e")
	}
	termArgs = append(termArgs, "bash", "-c")

	bashCommand := fmt.Sprintf("%s; echo; read -p \"%s\" -n1;", quote(args), WAIT_MESSAGE)
	termArgs = append(termArgs, fmt.Sprintf("''%s''", bashCommand)) // wrapping argument in two sets of ' because node is so "friendly" that it removes one set...

	// merge environment variables into a copy of the process.env
	env := getSanitizedEnvironment()
	for key, value := range envVars {
		env[key] = value
	}

	// delete environment variables that have a null value
	for key, value := range env {
		if value == "" {
			delete(env, key)
		}
	}

	cmd := exec.Command(execPath, termArgs...)
	cmd.Dir = dir
	cmd.Env = mapToEnvSlice(env)

	output, err := cmd.CombinedOutput()
	if err != nil {
		if len(output) > 0 {
			lines := strings.Split(string(output), "\n")
			if len(lines) > 0 {
				return nil, fmt.Errorf("%s", lines[0])
			}
		}
		return nil, fmt.Errorf("'%s' failed with exit code %d", execPath, cmd.ProcessState.ExitCode())
	}

	return nil, nil
}

// getDefaultTerminalLinuxReady returns the default Linux terminal
func (l *LinuxExternalTerminalService) getDefaultTerminalLinuxReady() (string, error) {
	defaultTerminalLinuxReadyOnce.Do(func() {
		if !common.IsLinux {
			defaultTerminalLinuxReady = "xterm"
			return
		}

		isDebian, err := basepfs.Promises.Exists("/etc/debian_version")
		if err != nil {
			defaultTerminalLinuxReadyError = err
			return
		}

		if isDebian {
			defaultTerminalLinuxReady = "x-terminal-emulator"
		} else if os.Getenv("DESKTOP_SESSION") == "gnome" || os.Getenv("DESKTOP_SESSION") == "gnome-classic" {
			defaultTerminalLinuxReady = "gnome-terminal"
		} else if os.Getenv("DESKTOP_SESSION") == "kde-plasma" {
			defaultTerminalLinuxReady = "konsole"
		} else if colorterm := os.Getenv("COLORTERM"); colorterm != "" {
			defaultTerminalLinuxReady = colorterm
		} else if term := os.Getenv("TERM"); term != "" {
			defaultTerminalLinuxReady = term
		} else {
			defaultTerminalLinuxReady = "xterm"
		}
	})

	return defaultTerminalLinuxReady, defaultTerminalLinuxReadyError
}

// spawnTerminal spawns a terminal process on Linux
func (l *LinuxExternalTerminalService) spawnTerminal(configuration *externalTerminalCommon.IExternalTerminalSettings, cwd *string) error {
	var execPath string
	var err error

	if configuration != nil && configuration.LinuxExec != nil {
		execPath = *configuration.LinuxExec
	} else {
		execPath, err = l.getDefaultTerminalLinuxReady()
		if err != nil {
			return err
		}
	}

	env := getSanitizedEnvironment()
	cmd := exec.Command(execPath)
	cmd.Dir = ""
	if cwd != nil {
		cmd.Dir = *cwd
	}
	cmd.Env = mapToEnvSlice(env)

	return cmd.Start()
}

// Helper functions

// getSanitizedEnvironment returns a sanitized copy of the process environment
func getSanitizedEnvironment() map[string]string {
	env := make(map[string]string)
	for _, envVar := range os.Environ() {
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) == 2 {
			env[parts[0]] = parts[1]
		}
	}
	common.SanitizeProcessEnvironment(env)
	return env
}

// improveError tries to turn OS errors into more meaningful error messages
func improveError(err error) error {
	if err == nil {
		return nil
	}

	// Check if it's a path not found error
	if strings.Contains(err.Error(), "no such file or directory") || strings.Contains(err.Error(), "cannot find the file") {
		return fmt.Errorf("can't find terminal application '%s'", err.Error())
	}
	return err
}

// quote quotes args if necessary and combines into a space separated string
func quote(args []string) string {
	var result strings.Builder
	for i, a := range args {
		if strings.Contains(a, " ") {
			result.WriteString("\"" + a + "\"")
		} else {
			result.WriteString(a)
		}
		if i < len(args)-1 {
			result.WriteString(" ")
		}
	}
	return result.String()
}

// mapToEnvSlice converts a map to environment slice format
func mapToEnvSlice(env map[string]string) []string {
	var result []string
	for key, value := range env {
		result = append(result, fmt.Sprintf("%s=%s", key, value))
	}
	return result
}

// mapToStringMap converts environment slice to string map
func mapToStringMap(environ []string) map[string]string {
	result := make(map[string]string)
	for _, envVar := range environ {
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) == 2 {
			result[parts[0]] = parts[1]
		}
	}
	return result
}