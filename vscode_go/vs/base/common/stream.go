/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"context"
	"fmt"
	"sync"
)

// ReadableStreamEventPayload represents the payload that flows in readable stream events
type ReadableStreamEventPayload[T any] interface{}

// ReadableStreamEvents interface for stream events
type ReadableStreamEvents[T any] interface {
	OnData(callback func(data T))
	OnError(callback func(err error))
	OnEnd(callback func())
}

// ReadableStream interface that emulates the API shape of a node.js readable stream
type ReadableStream[T any] interface {
	ReadableStreamEvents[T]
	Pause()
	Resume()
	Destroy()
	RemoveListener(event string, callback func(interface{}))
}

// Readable interface that emulates the API shape of a node.js readable
type Readable[T any] interface {
	Read() *T
}

// WriteableStream interface that emulates the API shape of a node.js writeable stream
type WriteableStream[T any] interface {
	ReadableStream[T]
	Write(data T) error
	Error(err error)
	End(result ...T)
}

// ReadableBufferedStream represents a stream that has a buffer already read
type ReadableBufferedStream[T any] struct {
	Stream ReadableStream[T]
	Buffer []T
	Ended  bool
}

// WriteableStreamOptions configuration for writeable streams
type WriteableStreamOptions struct {
	HighWaterMark *int
}

// IReducer function type for reducing data
type IReducer[T any, R any] func(data []T) R

// IDataTransformer function type for transforming data
type IDataTransformer[Original any, Transformed any] func(data Original) Transformed

// IErrorTransformer function type for transforming errors
type IErrorTransformer func(error) error

// ITransformer contains data and error transformers
type ITransformer[Original any, Transformed any] struct {
	Data  IDataTransformer[Original, Transformed]
	Error IErrorTransformer
}

// WriteableStreamImpl implements WriteableStream
type WriteableStreamImpl[T any] struct {
	mu                   sync.RWMutex
	state                writeableStreamState
	buffer               writeableStreamBuffer[T]
	listeners            writeableStreamListeners[T]
	pendingWritePromises []chan struct{}
	reducer              IReducer[T, T]
	options              *WriteableStreamOptions
}

type writeableStreamState struct {
	flowing   bool
	ended     bool
	destroyed bool
}

type writeableStreamBuffer[T any] struct {
	data   []T
	errors []error
	ended  bool
}

type writeableStreamListeners[T any] struct {
	data  []func(T)
	error []func(error)
	end   []func()
}

// NewWriteableStream creates a new writeable stream (matches TypeScript newWriteableStream API)
func NewWriteableStream[T any](reducer IReducer[T, T], options *WriteableStreamOptions) WriteableStream[T] {
	if reducer == nil {
		reducer = func(data []T) T {
			if len(data) > 0 {
				return data[len(data)-1]
			}
			var zero T
			return zero
		}
	}

	return &WriteableStreamImpl[T]{
		reducer: reducer,
		options: options,
		state: writeableStreamState{
			flowing:   false,
			ended:     false,
			destroyed: false,
		},
		buffer: writeableStreamBuffer[T]{
			data:   make([]T, 0),
			errors: make([]error, 0),
			ended:  false,
		},
		listeners: writeableStreamListeners[T]{
			data:  make([]func(T), 0),
			error: make([]func(error), 0),
			end:   make([]func(), 0),
		},
		pendingWritePromises: make([]chan struct{}, 0),
	}
}

// Pause stops emitting events
func (w *WriteableStreamImpl[T]) Pause() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.state.destroyed {
		return
	}

	w.state.flowing = false
}

// Resume starts emitting events again
func (w *WriteableStreamImpl[T]) Resume() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.state.destroyed {
		return
	}

	if !w.state.flowing {
		w.state.flowing = true
		// Flow queued data immediately
		go w.flowData()
	}
}

// Write writes data to the stream
func (w *WriteableStreamImpl[T]) Write(data T) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.state.destroyed || w.state.ended {
		return fmt.Errorf("stream is destroyed or ended")
	}

	// Check high water mark
	highWaterMark := 16
	if w.options != nil && w.options.HighWaterMark != nil {
		highWaterMark = *w.options.HighWaterMark
	}

	if !w.state.flowing {
		w.buffer.data = append(w.buffer.data, data)

		// Return promise if buffer is full
		if len(w.buffer.data) >= highWaterMark {
			ch := make(chan struct{})
			w.pendingWritePromises = append(w.pendingWritePromises, ch)
			go func() {
				<-ch
			}()
		}
	} else {
		w.emitData(data)
	}

	return nil
}

// Error signals an error to the stream
func (w *WriteableStreamImpl[T]) Error(err error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.state.destroyed {
		return
	}

	if !w.state.flowing {
		w.buffer.errors = append(w.buffer.errors, err)
	} else {
		w.emitError(err)
	}
}

// End signals the end of the stream
func (w *WriteableStreamImpl[T]) End(result ...T) {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.state.destroyed {
		return
	}

	// Emit result if provided
	if len(result) > 0 {
		if !w.state.flowing {
			w.buffer.data = append(w.buffer.data, result[0])
		} else {
			w.emitData(result[0])
		}
	}

	// End the stream
	if !w.state.flowing {
		w.buffer.ended = true
	} else {
		w.emitEnd()
	}

	w.state.ended = true
}

// emitData emits data to listeners
func (w *WriteableStreamImpl[T]) emitData(data T) {
	for _, callback := range w.listeners.data {
		callback(data)
	}
}

// emitError emits error to listeners
func (w *WriteableStreamImpl[T]) emitError(err error) {
	if len(w.listeners.error) > 0 {
		for _, callback := range w.listeners.error {
			callback(err)
		}
	} else {
		// If no error listeners, this would typically panic in Node.js
		go func() {
			defer func() {
				if r := recover(); r != nil {
					// Ignore panics from unhandled errors
				}
			}()
			OnUnexpectedError(err)
		}()
	}
}

// emitEnd emits end to listeners
func (w *WriteableStreamImpl[T]) emitEnd() {
	for _, callback := range w.listeners.end {
		callback()
	}
}

// OnData adds a data event listener
func (w *WriteableStreamImpl[T]) OnData(callback func(data T)) {
	w.mu.Lock()
	defer w.mu.Unlock()

	w.listeners.data = append(w.listeners.data, callback)

	// Start flowing if this is the first data listener
	if !w.state.flowing && len(w.listeners.data) == 1 {
		w.state.flowing = true
		go w.flowData()
	}
}

// OnError adds an error event listener
func (w *WriteableStreamImpl[T]) OnError(callback func(err error)) {
	w.mu.Lock()
	defer w.mu.Unlock()

	w.listeners.error = append(w.listeners.error, callback)

	// Flow any queued errors
	if w.state.flowing {
		go w.flowErrors()
	}
}

// OnEnd adds an end event listener
func (w *WriteableStreamImpl[T]) OnEnd(callback func()) {
	w.mu.Lock()
	defer w.mu.Unlock()

	w.listeners.end = append(w.listeners.end, callback)

	// If already ended and flowing, emit end immediately
	if w.state.flowing && w.buffer.ended {
		go callback()
	}
}

// RemoveListener removes a listener
func (w *WriteableStreamImpl[T]) RemoveListener(event string, callback func(interface{})) {
	w.mu.Lock()
	defer w.mu.Unlock()

	// This is a simplified implementation
	// In a real implementation, you'd need to match the actual callback function
}

// Destroy destroys the stream
func (w *WriteableStreamImpl[T]) Destroy() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.state.destroyed {
		return
	}

	w.state.destroyed = true
	w.state.flowing = false
	w.state.ended = true

	// Clear all data
	w.buffer.data = w.buffer.data[:0]
	w.buffer.errors = w.buffer.errors[:0]

	// Clear all listeners
	w.listeners.data = w.listeners.data[:0]
	w.listeners.error = w.listeners.error[:0]
	w.listeners.end = w.listeners.end[:0]

	// Resolve pending write promises
	for _, ch := range w.pendingWritePromises {
		close(ch)
	}
	w.pendingWritePromises = w.pendingWritePromises[:0]
}

// flowData flows buffered data
func (w *WriteableStreamImpl[T]) flowData() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.state.flowing || w.state.destroyed {
		return
	}

	// Emit all buffered data
	if len(w.buffer.data) > 0 {
		var reduced T
		if w.reducer != nil {
			reduced = w.reducer(w.buffer.data)
		} else if len(w.buffer.data) > 0 {
			reduced = w.buffer.data[len(w.buffer.data)-1]
		}

		// Clear buffer and emit reduced data
		w.buffer.data = w.buffer.data[:0]

		// Resolve pending promises
		for _, ch := range w.pendingWritePromises {
			close(ch)
		}
		w.pendingWritePromises = w.pendingWritePromises[:0]

		w.emitData(reduced)
	}

	// Flow errors
	w.flowErrors()

	// Flow end if needed
	if w.buffer.ended {
		w.emitEnd()
	}
}

// flowErrors flows buffered errors
func (w *WriteableStreamImpl[T]) flowErrors() {
	for _, err := range w.buffer.errors {
		w.emitError(err)
	}
	w.buffer.errors = w.buffer.errors[:0]
}

// Utility functions

// IsReadable checks if an object is readable
func IsReadable[T any](obj interface{}) bool {
	_, ok := obj.(Readable[T])
	return ok
}

// IsReadableStream checks if an object is a readable stream
func IsGenericReadableStream[T any](obj interface{}) bool {
	_, ok := obj.(ReadableStream[T])
	return ok
}

// IsReadableBufferedStream checks if an object is a readable buffered stream
func IsGenericReadableBufferedStream[T any](obj interface{}) bool {
	_, ok := obj.(*ReadableBufferedStream[T])
	return ok
}

// ConsumeReadable consumes data from a readable
func ConsumeReadable[T any, R any](readable Readable[T], reducer IReducer[T, R]) R {
	var data []T

	for {
		item := readable.Read()
		if item == nil {
			break
		}
		data = append(data, *item)
	}

	return reducer(data)
}

// PeekReadable peeks at a readable without consuming
func PeekGenericReadable[T any, R any](readable Readable[T], reducer IReducer[T, R], maxChunks int) interface{} {
	var data []T

	for i := 0; i < maxChunks; i++ {
		item := readable.Read()
		if item == nil {
			break
		}
		data = append(data, *item)
	}

	if len(data) == maxChunks {
		// Return the readable for further consumption
		return readable
	}

	// Return the reduced data
	return reducer(data)
}

// ConsumeStream consumes a stream
func ConsumeGenericStream[T any, R any](stream ReadableStreamEvents[T], reducer IReducer[T, R]) <-chan R {
	result := make(chan R, 1)
	var data []T

	stream.OnData(func(chunk T) {
		data = append(data, chunk)
	})

	stream.OnError(func(err error) {
		// For now, just ignore errors in the consumption
		// In a real implementation, you might want to handle this differently
	})

	stream.OnEnd(func() {
		result <- reducer(data)
		close(result)
	})

	return result
}

// IStreamListener interface for stream listeners
type IStreamListener[T any] interface {
	OnData(data T)
	OnError(err error)
	OnEnd()
}

// ListenStream listens to stream events
func ListenGenericStream[T any](stream ReadableStreamEvents[T], listener IStreamListener[T], ctx context.Context) {
	if ctx == nil {
		ctx = context.Background()
	}

	stream.OnData(func(data T) {
		select {
		case <-ctx.Done():
			return
		default:
			listener.OnData(data)
		}
	})

	stream.OnError(func(err error) {
		select {
		case <-ctx.Done():
			return
		default:
			listener.OnError(err)
		}
	})

	stream.OnEnd(func() {
		select {
		case <-ctx.Done():
			return
		default:
			listener.OnEnd()
		}
	})
}

// PeekStream peeks at a stream
func PeekGenericStream[T any](stream ReadableStream[T], maxChunks int) <-chan *ReadableBufferedStream[T] {
	result := make(chan *ReadableBufferedStream[T], 1)

	bufferedStream := &ReadableBufferedStream[T]{
		Stream: stream,
		Buffer: make([]T, 0, maxChunks),
		Ended:  false,
	}

	dataCount := 0

	stream.OnData(func(chunk T) {
		if dataCount < maxChunks {
			bufferedStream.Buffer = append(bufferedStream.Buffer, chunk)
			dataCount++

			if dataCount == maxChunks {
				stream.Pause()
				result <- bufferedStream
				close(result)
			}
		}
	})

	stream.OnEnd(func() {
		bufferedStream.Ended = true
		if dataCount < maxChunks {
			result <- bufferedStream
			close(result)
		}
	})

	stream.OnError(func(err error) {
		// Handle error - for now just end the stream
		bufferedStream.Ended = true
		result <- bufferedStream
		close(result)
	})

	return result
}

// ToStream converts a value to a stream
func ToStream[T any](value T, reducer IReducer[T, T]) ReadableStream[T] {
	stream := NewWriteableStream(reducer, nil)

	go func() {
		stream.Write(value)
		stream.End()
	}()

	return stream
}

// EmptyStream returns an empty stream
func EmptyStream[T any]() ReadableStream[T] {
	stream := NewWriteableStream[T](nil, nil)
	go func() {
		stream.End()
	}()
	return stream
}

// ToReadable converts a value to a readable
func ToReadable[T any](value T) Readable[T] {
	return &simpleReadable[T]{
		value: &value,
		read:  false,
	}
}

type simpleReadable[T any] struct {
	value *T
	read  bool
}

func (r *simpleReadable[T]) Read() *T {
	if r.read {
		return nil
	}
	r.read = true
	return r.value
}

// Transform transforms a stream
func TransformGeneric[Original any, Transformed any](
	stream ReadableStreamEvents[Original],
	transformer ITransformer[Original, Transformed],
	reducer IReducer[Transformed, Transformed],
) ReadableStream[Transformed] {

	transformedStream := NewWriteableStream(reducer, nil)

	stream.OnData(func(data Original) {
		transformed := transformer.Data(data)
		transformedStream.Write(transformed)
	})

	stream.OnError(func(err error) {
		if transformer.Error != nil {
			err = transformer.Error(err)
		}
		transformedStream.Error(err)
	})

	stream.OnEnd(func() {
		transformedStream.End()
	})

	return transformedStream
}

// PrefixedReadable creates a readable with a prefix
func PrefixedReadable[T any](prefix T, readable Readable[T], reducer IReducer[T, T]) Readable[T] {
	return &prefixedReadable[T]{
		prefix:     &prefix,
		readable:   readable,
		prefixRead: false,
	}
}

type prefixedReadable[T any] struct {
	prefix     *T
	readable   Readable[T]
	prefixRead bool
}

func (r *prefixedReadable[T]) Read() *T {
	if !r.prefixRead {
		r.prefixRead = true
		return r.prefix
	}
	return r.readable.Read()
}

// PrefixedStream creates a stream with a prefix
func PrefixedStream[T any](prefix T, stream ReadableStream[T], reducer IReducer[T, T]) ReadableStream[T] {
	prefixedStream := NewWriteableStream(reducer, nil)

	go func() {
		prefixedStream.Write(prefix)

		stream.OnData(func(data T) {
			prefixedStream.Write(data)
		})

		stream.OnError(func(err error) {
			prefixedStream.Error(err)
		})

		stream.OnEnd(func() {
			prefixedStream.End()
		})
	}()

	return prefixedStream
}
