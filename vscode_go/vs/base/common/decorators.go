/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sync"
)

// MemoizeFunc represents a function that can be memoized
type MemoizeFunc[T any] func() T

// MemoizedFunction represents a memoized function
type MemoizedFunction[T any] struct {
	fn     MemoizeFunc[T]
	result T
	called bool
	mutex  sync.Mutex
}

// NewMemoizedFunction creates a new memoized function
func NewMemoizedFunction[T any](fn MemoizeFunc[T]) *MemoizedFunction[T] {
	return &MemoizedFunction[T]{
		fn:     fn,
		called: false,
	}
}

// Call executes the memoized function, returning cached result if already called
func (m *MemoizedFunction[T]) Call() T {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.called {
		m.result = m.fn()
		m.called = true
	}
	return m.result
}

// Reset clears the memoized result, forcing the function to be called again
func (m *MemoizedFunction[T]) Reset() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.called = false
	var zero T
	m.result = zero
}

// Memoize creates a memoized version of the given function
func Memoize[T any](fn MemoizeFunc[T]) func() T {
	memoized := NewMemoizedFunction(fn)
	return memoized.Call
}

// MemoizeAsync represents an async memoized function
type MemoizeAsync[T any] struct {
	fn     func() (T, error)
	result T
	err    error
	called bool
	mutex  sync.Mutex
}

// NewMemoizeAsync creates a new async memoized function
func NewMemoizeAsync[T any](fn func() (T, error)) *MemoizeAsync[T] {
	return &MemoizeAsync[T]{
		fn:     fn,
		called: false,
	}
}

// Call executes the async memoized function
func (m *MemoizeAsync[T]) Call() (T, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.called {
		m.result, m.err = m.fn()
		m.called = true
	}
	return m.result, m.err
}

// Reset clears the memoized result
func (m *MemoizeAsync[T]) Reset() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.called = false
	var zero T
	m.result = zero
	m.err = nil
}

// MemoizeAsyncFunc creates an async memoized version of the given function
func MemoizeAsyncFunc[T any](fn func() (T, error)) func() (T, error) {
	memoized := NewMemoizeAsync(fn)
	return memoized.Call
}