/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"bytes"
	"sync"
)

// VSBuffer represents a buffer with byte data
type VSBuffer struct {
	buffer []byte
}

// NewVSBuffer creates a new VSBuffer from byte slice
func NewVSBuffer(buffer []byte) *VSBuffer {
	if buffer == nil {
		buffer = make([]byte, 0)
	}
	return &VSBuffer{buffer: buffer}
}

// NewVSBufferWithCapacity creates a new VSBuffer with specified capacity
func NewVSBufferWithCapacity(capacity int) *VSBuffer {
	return &VSBuffer{buffer: make([]byte, 0, capacity)}
}

// FromString creates a VSBuffer from a string
func VSBufferFromString(str string) *VSBuffer {
	return &VSBuffer{buffer: []byte(str)}
}

// Alloc creates a VSBuffer with the specified size
func VSBufferAlloc(size int) *VSBuffer {
	return &VSBuffer{buffer: make([]byte, size)}
}

// Wrap wraps an existing byte slice
func VSBufferWrap(buffer []byte) *VSBuffer {
	return &VSBuffer{buffer: buffer}
}

// Concat concatenates multiple VSBuffers
func VSBufferConcat(buffers []*VSBuffer) *VSBuffer {
	if len(buffers) == 0 {
		return NewVSBuffer(nil)
	}

	if len(buffers) == 1 {
		return buffers[0]
	}

	totalSize := 0
	for _, buf := range buffers {
		totalSize += buf.ByteLength()
	}

	result := make([]byte, 0, totalSize)
	for _, buf := range buffers {
		result = append(result, buf.buffer...)
	}

	return &VSBuffer{buffer: result}
}

// Buffer returns the underlying byte slice
func (vb *VSBuffer) Buffer() []byte {
	return vb.buffer
}

// ByteLength returns the length of the buffer in bytes
func (vb *VSBuffer) ByteLength() int {
	return len(vb.buffer)
}

// ToString converts the buffer to a string
func (vb *VSBuffer) ToString() string {
	return string(vb.buffer)
}

// Slice returns a slice of the buffer
func (vb *VSBuffer) Slice(start, end int) *VSBuffer {
	if start < 0 {
		start = 0
	}
	if end > len(vb.buffer) {
		end = len(vb.buffer)
	}
	if start > end {
		start = end
	}

	return &VSBuffer{buffer: vb.buffer[start:end]}
}

// Set sets a byte at the specified index
func (vb *VSBuffer) Set(index int, value byte) {
	if index >= 0 && index < len(vb.buffer) {
		vb.buffer[index] = value
	}
}

// Get gets a byte at the specified index
func (vb *VSBuffer) Get(index int) byte {
	if index >= 0 && index < len(vb.buffer) {
		return vb.buffer[index]
	}
	return 0
}

// Equals checks if two buffers are equal
func (vb *VSBuffer) Equals(other *VSBuffer) bool {
	if vb == other {
		return true
	}
	if other == nil {
		return false
	}
	return bytes.Equal(vb.buffer, other.buffer)
}

// Clone creates a copy of the buffer
func (vb *VSBuffer) Clone() *VSBuffer {
	clone := make([]byte, len(vb.buffer))
	copy(clone, vb.buffer)
	return &VSBuffer{buffer: clone}
}

// Append appends data to the buffer
func (vb *VSBuffer) Append(data []byte) {
	vb.buffer = append(vb.buffer, data...)
}

// AppendBuffer appends another VSBuffer to this buffer
func (vb *VSBuffer) AppendBuffer(other *VSBuffer) {
	if other != nil {
		vb.buffer = append(vb.buffer, other.buffer...)
	}
}

// VSBufferReadable represents a readable stream of VSBuffer chunks
type VSBufferReadable interface {
	Read() *VSBuffer
}

// VSBufferReadableStream represents a readable stream with events
type VSBufferReadableStream interface {
	On(event string, handler func(...interface{}))
	Pause()
	Resume()
	Destroy()
	Read() *VSBuffer
}

// VSBufferReadableBufferedStream represents a buffered readable stream
type VSBufferReadableBufferedStream interface {
	VSBufferReadableStream
	Buffer() []*VSBuffer
	Ended() bool
	Stream() VSBufferReadableStream
}

// ArrayBufferReadable implements VSBufferReadable for byte slice
type ArrayBufferReadable struct {
	buffer []byte
	pos    int
	mu     sync.Mutex
}

// NewArrayBufferReadable creates a new ArrayBufferReadable
func NewArrayBufferReadable(buffer []byte) *ArrayBufferReadable {
	return &ArrayBufferReadable{
		buffer: buffer,
		pos:    0,
	}
}

// Read reads data from the buffer
func (abr *ArrayBufferReadable) Read() *VSBuffer {
	abr.mu.Lock()
	defer abr.mu.Unlock()

	if abr.pos >= len(abr.buffer) {
		return nil
	}

	// Read in chunks (e.g., 64KB at a time)
	chunkSize := 65536
	if abr.pos+chunkSize > len(abr.buffer) {
		chunkSize = len(abr.buffer) - abr.pos
	}

	chunk := abr.buffer[abr.pos : abr.pos+chunkSize]
	abr.pos += chunkSize

	return VSBufferWrap(chunk)
}

// SimpleReadableStream implements VSBufferReadableStream
type SimpleReadableStream struct {
	readable VSBufferReadable
	paused   bool
	ended    bool
	handlers map[string][]func(...interface{})
	mu       sync.RWMutex
}

// NewSimpleReadableStream creates a new SimpleReadableStream
func NewSimpleReadableStream(readable VSBufferReadable) *SimpleReadableStream {
	return &SimpleReadableStream{
		readable: readable,
		handlers: make(map[string][]func(...interface{})),
	}
}

// On adds an event handler
func (srs *SimpleReadableStream) On(event string, handler func(...interface{})) {
	srs.mu.Lock()
	defer srs.mu.Unlock()

	if srs.handlers[event] == nil {
		srs.handlers[event] = make([]func(...interface{}), 0)
	}
	srs.handlers[event] = append(srs.handlers[event], handler)
}

// Emit emits an event
func (srs *SimpleReadableStream) Emit(event string, args ...interface{}) {
	srs.mu.RLock()
	handlers := make([]func(...interface{}), len(srs.handlers[event]))
	copy(handlers, srs.handlers[event])
	srs.mu.RUnlock()

	for _, handler := range handlers {
		handler(args...)
	}
}

// Pause pauses the stream
func (srs *SimpleReadableStream) Pause() {
	srs.mu.Lock()
	defer srs.mu.Unlock()
	srs.paused = true
}

// Resume resumes the stream
func (srs *SimpleReadableStream) Resume() {
	srs.mu.Lock()
	defer srs.mu.Unlock()
	srs.paused = false

	// Start reading if not ended
	if !srs.ended {
		go srs.readLoop()
	}
}

// Destroy destroys the stream
func (srs *SimpleReadableStream) Destroy() {
	srs.mu.Lock()
	defer srs.mu.Unlock()

	srs.ended = true
	srs.Emit("close")
}

// Read reads a chunk from the stream
func (srs *SimpleReadableStream) Read() *VSBuffer {
	srs.mu.RLock()
	readable := srs.readable
	srs.mu.RUnlock()

	if readable != nil {
		return readable.Read()
	}
	return nil
}

func (srs *SimpleReadableStream) readLoop() {
	for {
		srs.mu.RLock()
		if srs.paused || srs.ended {
			srs.mu.RUnlock()
			return
		}
		srs.mu.RUnlock()

		chunk := srs.Read()
		if chunk == nil {
			srs.mu.Lock()
			srs.ended = true
			srs.mu.Unlock()
			srs.Emit("end")
			return
		}

		srs.Emit("data", chunk)
	}
}

// BufferedReadableStream implements VSBufferReadableBufferedStream
type BufferedReadableStream struct {
	buffer []*VSBuffer
	stream VSBufferReadableStream
	ended  bool
	mu     sync.RWMutex
}

// NewBufferedReadableStream creates a new BufferedReadableStream
func NewBufferedReadableStream(stream VSBufferReadableStream, buffer []*VSBuffer) *BufferedReadableStream {
	return &BufferedReadableStream{
		buffer: buffer,
		stream: stream,
		ended:  false,
	}
}

// Buffer returns the buffered data
func (brs *BufferedReadableStream) Buffer() []*VSBuffer {
	brs.mu.RLock()
	defer brs.mu.RUnlock()

	result := make([]*VSBuffer, len(brs.buffer))
	copy(result, brs.buffer)
	return result
}

// Ended returns true if the stream has ended
func (brs *BufferedReadableStream) Ended() bool {
	brs.mu.RLock()
	defer brs.mu.RUnlock()
	return brs.ended
}

// Stream returns the underlying stream
func (brs *BufferedReadableStream) Stream() VSBufferReadableStream {
	brs.mu.RLock()
	defer brs.mu.RUnlock()
	return brs.stream
}

// On forwards to the underlying stream
func (brs *BufferedReadableStream) On(event string, handler func(...interface{})) {
	if brs.stream != nil {
		brs.stream.On(event, handler)
	}
}

// Pause forwards to the underlying stream
func (brs *BufferedReadableStream) Pause() {
	if brs.stream != nil {
		brs.stream.Pause()
	}
}

// Resume forwards to the underlying stream
func (brs *BufferedReadableStream) Resume() {
	if brs.stream != nil {
		brs.stream.Resume()
	}
}

// Destroy forwards to the underlying stream
func (brs *BufferedReadableStream) Destroy() {
	if brs.stream != nil {
		brs.stream.Destroy()
	}
}

// Read forwards to the underlying stream
func (brs *BufferedReadableStream) Read() *VSBuffer {
	if brs.stream != nil {
		return brs.stream.Read()
	}
	return nil
}

// WriteableBufferStream represents a writeable buffer stream
type WriteableBufferStream struct {
	chunks   []*VSBuffer
	ended    bool
	handlers map[string][]func(...interface{})
	mu       sync.RWMutex
}

// Write writes data to the stream
func (wbs *WriteableBufferStream) Write(chunk *VSBuffer) bool {
	wbs.mu.Lock()
	defer wbs.mu.Unlock()

	if wbs.ended {
		return false
	}

	if chunk != nil {
		wbs.chunks = append(wbs.chunks, chunk)
	}

	// Emit data event
	wbs.emitNoLock("data", chunk)
	return true
}

// End ends the stream
func (wbs *WriteableBufferStream) End(chunk *VSBuffer) {
	wbs.mu.Lock()
	defer wbs.mu.Unlock()

	if wbs.ended {
		return
	}

	if chunk != nil {
		wbs.chunks = append(wbs.chunks, chunk)
		wbs.emitNoLock("data", chunk)
	}

	wbs.ended = true
	wbs.emitNoLock("end")
}

// On adds an event handler
func (wbs *WriteableBufferStream) On(event string, handler func(...interface{})) {
	wbs.mu.Lock()
	defer wbs.mu.Unlock()

	if wbs.handlers[event] == nil {
		wbs.handlers[event] = make([]func(...interface{}), 0)
	}
	wbs.handlers[event] = append(wbs.handlers[event], handler)
}

// Emit emits an event
func (wbs *WriteableBufferStream) Emit(event string, args ...interface{}) {
	wbs.mu.RLock()
	defer wbs.mu.RUnlock()
	wbs.emitNoLock(event, args...)
}

func (wbs *WriteableBufferStream) emitNoLock(event string, args ...interface{}) {
	handlers := wbs.handlers[event]
	for _, handler := range handlers {
		handler(args...)
	}
}

// Buffer returns all buffered chunks concatenated
func (wbs *WriteableBufferStream) Buffer() *VSBuffer {
	wbs.mu.RLock()
	defer wbs.mu.RUnlock()

	return VSBufferConcat(wbs.chunks)
}

// Pause pauses the stream (no-op for writeable streams)
func (wbs *WriteableBufferStream) Pause() {
	// No-op for writeable streams
}

// Resume resumes the stream (no-op for writeable streams)
func (wbs *WriteableBufferStream) Resume() {
	// No-op for writeable streams
}

// Destroy destroys the stream
func (wbs *WriteableBufferStream) Destroy() {
	wbs.mu.Lock()
	defer wbs.mu.Unlock()

	if !wbs.ended {
		wbs.ended = true
		wbs.emitNoLock("close")
	}
}

// Read reads data from the stream (returns nil for writeable streams)
func (wbs *WriteableBufferStream) Read() *VSBuffer {
	// Writeable streams don't support reading
	return nil
}

// Utility functions

// BufferToReadable converts a VSBuffer to a VSBufferReadable
func BufferToReadable(buffer *VSBuffer) VSBufferReadable {
	if buffer == nil {
		return NewArrayBufferReadable(nil)
	}
	return NewArrayBufferReadable(buffer.Buffer())
}

// ReadableToBuffer converts a VSBufferReadable to a VSBuffer
func ReadableToBuffer(readable VSBufferReadable) *VSBuffer {
	if readable == nil {
		return NewVSBuffer(nil)
	}

	chunks := make([]*VSBuffer, 0)
	for {
		chunk := readable.Read()
		if chunk == nil {
			break
		}
		chunks = append(chunks, chunk)
	}

	return VSBufferConcat(chunks)
}

// StreamToBuffer converts a VSBufferReadableStream to a VSBuffer asynchronously
func StreamToBuffer(stream VSBufferReadableStream) *VSBuffer {
	if stream == nil {
		return NewVSBuffer(nil)
	}

	chunks := make([]*VSBuffer, 0)
	done := make(chan bool)

	stream.On("data", func(args ...interface{}) {
		if len(args) > 0 {
			if chunk, ok := args[0].(*VSBuffer); ok {
				chunks = append(chunks, chunk)
			}
		}
	})

	stream.On("end", func(args ...interface{}) {
		done <- true
	})

	stream.On("error", func(args ...interface{}) {
		done <- true
	})

	// Wait for completion
	<-done

	return VSBufferConcat(chunks)
}

// BufferedStreamToBuffer converts a buffered stream to a buffer
func BufferedStreamToBuffer(stream VSBufferReadableBufferedStream) *VSBuffer {
	if stream == nil {
		return NewVSBuffer(nil)
	}

	bufferedChunks := stream.Buffer()

	if stream.Ended() {
		return VSBufferConcat(bufferedChunks)
	}

	// If not ended, read the remaining stream
	streamBuffer := StreamToBuffer(stream.Stream())
	allChunks := append(bufferedChunks, streamBuffer)

	return VSBufferConcat(allChunks)
}

// PeekReadable peeks at a readable and returns a buffered version
func PeekReadable(readable VSBufferReadable, processor func([]*VSBuffer) *VSBuffer, maxChunks int) interface{} {
	if readable == nil {
		return NewVSBuffer(nil)
	}

	chunks := make([]*VSBuffer, 0, maxChunks)
	for i := 0; i < maxChunks; i++ {
		chunk := readable.Read()
		if chunk == nil {
			break
		}
		chunks = append(chunks, chunk)
	}

	if processor != nil {
		return processor(chunks)
	}

	return VSBufferConcat(chunks)
}

// Transform creates a transform stream
func Transform(
	stream VSBufferReadableStream,
	transformer struct {
		Data  func(*VSBuffer) *VSBuffer
		Error func(error) error
	},
	reducer func([]*VSBuffer) *VSBuffer,
) VSBufferReadableStream {
	writeableStream := &WriteableBufferStream{
		chunks:   make([]*VSBuffer, 0),
		handlers: make(map[string][]func(...interface{})),
	}

	if stream == nil {
		writeableStream.End(nil)
		return writeableStream
	}

	stream.On("data", func(args ...interface{}) {
		if len(args) > 0 {
			if chunk, ok := args[0].(*VSBuffer); ok {
				if transformer.Data != nil {
					transformed := transformer.Data(chunk)
					writeableStream.Write(transformed)
				} else {
					writeableStream.Write(chunk)
				}
			}
		}
	})

	stream.On("end", func(args ...interface{}) {
		writeableStream.End(nil)
	})

	stream.On("error", func(args ...interface{}) {
		writeableStream.End(nil)
	})

	return writeableStream
}

// NewWriteableBufferStream creates a writeable stream for VSBuffer (matches TypeScript newWriteableBufferStream)
func NewWriteableBufferStream(options *WriteableStreamOptions) WriteableStream[*VSBuffer] {
	return NewWriteableStream(func(chunks []*VSBuffer) *VSBuffer {
		return VSBufferConcat(chunks)
	}, options)
}

// Constants for buffer operations
const (
	DefaultChunkSize = 65536 // 64KB
)

// IsReadableStream checks if an object is a readable stream
func IsReadableStream(obj interface{}) bool {
	_, ok := obj.(VSBufferReadableStream)
	return ok
}

// IsReadableBufferedStream checks if an object is a readable buffered stream
func IsReadableBufferedStream(obj interface{}) bool {
	_, ok := obj.(VSBufferReadableBufferedStream)
	return ok
}

// ConsumeStream consumes a stream and returns all data
func ConsumeStream(stream VSBufferReadableStream) *VSBuffer {
	return StreamToBuffer(stream)
}

// ListenStream sets up listeners for a stream
func ListenStream(stream VSBufferReadableStream, listeners struct {
	OnData  func(*VSBuffer)
	OnError func(error)
	OnEnd   func()
}) {
	if stream == nil {
		return
	}

	if listeners.OnData != nil {
		stream.On("data", func(args ...interface{}) {
			if len(args) > 0 {
				if chunk, ok := args[0].(*VSBuffer); ok {
					listeners.OnData(chunk)
				}
			}
		})
	}

	if listeners.OnError != nil {
		stream.On("error", func(args ...interface{}) {
			if len(args) > 0 {
				if err, ok := args[0].(error); ok {
					listeners.OnError(err)
				}
			}
		})
	}

	if listeners.OnEnd != nil {
		stream.On("end", func(args ...interface{}) {
			listeners.OnEnd()
		})
	}
}

// PeekStream peeks at a stream and returns a buffered version
func PeekStream(stream VSBufferReadableStream, maxChunks int) VSBufferReadableBufferedStream {
	if stream == nil {
		return NewBufferedReadableStream(nil, []*VSBuffer{})
	}

	buffer := make([]*VSBuffer, 0, maxChunks)
	count := 0

	// Create a channel to collect initial chunks
	chunkChan := make(chan *VSBuffer, maxChunks)
	endChan := make(chan bool, 1)

	stream.On("data", func(args ...interface{}) {
		if count < maxChunks {
			if len(args) > 0 {
				if chunk, ok := args[0].(*VSBuffer); ok {
					chunkChan <- chunk
					count++
				}
			}
		}
	})

	stream.On("end", func(args ...interface{}) {
		endChan <- true
	})

	// Collect initial chunks
	go func() {
		for {
			select {
			case chunk := <-chunkChan:
				buffer = append(buffer, chunk)
			case <-endChan:
				return
			}

			if len(buffer) >= maxChunks {
				return
			}
		}
	}()

	return NewBufferedReadableStream(stream, buffer)
}
