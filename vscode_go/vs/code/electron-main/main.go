/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/lifecycle"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state"
)

// Main represents the main entry point for VS Code electron-main process
// This is the Go equivalent of main.ts in the TypeScript implementation
type Main struct {
	// Core services (equivalent to main.ts service initialization)
	environmentService    environment.IEnvironmentMainService
	configurationService  configuration.IConfigurationService
	lifecycleService      lifecycle.ILifecycleMainService
	logService           log.ILogService
	loggerService        log.ILoggerService
	fileService          files.IFileService
	productService       product.IProductService
	stateService         state.IStateService
	instantiationService instantiation.IInstantiationService
	
	// Application instance
	codeApplication *CodeApplication
	
	// Process management
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex
}

// NewMain creates a new main instance
// Equivalent to the main() function in main.ts
func NewMain() (*Main, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	main := &Main{
		ctx:    ctx,
		cancel: cancel,
	}
	
	// Initialize services (equivalent to main.ts service setup)
	if err := main.initializeServices(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize services: %w", err)
	}
	
	return main, nil
}

// initializeServices sets up the core services
// Equivalent to the service initialization in main.ts
func (m *Main) initializeServices() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	log.Println("🔧 Initializing VS Code main process services...")
	
	// Environment service (equivalent to EnvironmentMainService)
	envService, err := environment.NewEnvironmentMainService()
	if err != nil {
		return fmt.Errorf("failed to create environment service: %w", err)
	}
	m.environmentService = envService
	
	// Product service (equivalent to product import)
	m.productService = product.NewProductService()
	
	// Configuration service (equivalent to ConfigurationService)
	configService, err := configuration.NewConfigurationService()
	if err != nil {
		return fmt.Errorf("failed to create configuration service: %w", err)
	}
	m.configurationService = configService
	
	// Log service (equivalent to ConsoleMainLogger setup)
	logService, err := log.NewLogService()
	if err != nil {
		return fmt.Errorf("failed to create log service: %w", err)
	}
	m.logService = logService
	m.loggerService = logService // In simplified implementation, same service
	
	// File service (equivalent to FileService with DiskFileSystemProvider)
	fileService, err := files.NewFileService()
	if err != nil {
		return fmt.Errorf("failed to create file service: %w", err)
	}
	m.fileService = fileService
	
	// State service (equivalent to StateService)
	stateService, err := state.NewStateService(m.environmentService.GetUserDataPath())
	if err != nil {
		return fmt.Errorf("failed to create state service: %w", err)
	}
	m.stateService = stateService
	
	// Lifecycle service (equivalent to LifecycleMainService)
	lifecycleService, err := lifecycle.NewLifecycleMainService(m.environmentService, m.logService)
	if err != nil {
		return fmt.Errorf("failed to create lifecycle service: %w", err)
	}
	m.lifecycleService = lifecycleService
	
	// Instantiation service (equivalent to InstantiationService)
	instantiationService, err := instantiation.NewInstantiationService()
	if err != nil {
		return fmt.Errorf("failed to create instantiation service: %w", err)
	}
	m.instantiationService = instantiationService
	
	log.Println("✅ VS Code main process services initialized")
	return nil
}

// Startup starts the VS Code application
// Equivalent to the main startup sequence in main.ts
func (m *Main) Startup() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	log.Println("🚀 Starting VS Code main process...")
	
	// Set unexpected error handler (equivalent to setUnexpectedErrorHandler)
	m.setUnexpectedErrorHandler()
	
	// Performance mark (equivalent to mark('code/didStartMain'))
	startTime := time.Now()
	
	// Create CodeApplication instance (equivalent to new CodeApplication())
	codeApp, err := NewCodeApplication(
		m.environmentService,
		m.logService,
		m.loggerService,
		m.configurationService,
		m.lifecycleService,
		m.fileService,
		m.productService,
		m.stateService,
		m.instantiationService,
	)
	if err != nil {
		return fmt.Errorf("failed to create code application: %w", err)
	}
	m.codeApplication = codeApp
	
	// Start the application (equivalent to app.startup())
	if err := m.codeApplication.Startup(); err != nil {
		return fmt.Errorf("failed to start code application: %w", err)
	}
	
	log.Printf("✅ VS Code main process started in %v", time.Since(startTime))
	return nil
}

// setUnexpectedErrorHandler sets up error handling
// Equivalent to setUnexpectedErrorHandler in main.ts
func (m *Main) setUnexpectedErrorHandler() {
	// In Go, we'll use recover() in critical goroutines
	// This is a simplified implementation
	log.Println("🛡️ Setting up error handling...")
}

// Shutdown gracefully shuts down the main process
func (m *Main) Shutdown() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	log.Println("🔄 Shutting down VS Code main process...")
	
	if m.codeApplication != nil {
		if err := m.codeApplication.Shutdown(); err != nil {
			log.Printf("Error shutting down code application: %v", err)
		}
	}
	
	if m.lifecycleService != nil {
		m.lifecycleService.Shutdown()
	}
	
	m.cancel()
	
	log.Println("✅ VS Code main process shut down")
	return nil
}

// GetCodeApplication returns the CodeApplication instance
func (m *Main) GetCodeApplication() *CodeApplication {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.codeApplication
}

// Context returns the main context
func (m *Main) Context() context.Context {
	return m.ctx
}

// GetEnvironmentService returns the environment service
func (m *Main) GetEnvironmentService() environment.IEnvironmentMainService {
	return m.environmentService
}

// GetLogService returns the log service
func (m *Main) GetLogService() log.ILogService {
	return m.logService
}

// GetProductService returns the product service
func (m *Main) GetProductService() product.IProductService {
	return m.productService
}

// Helper functions equivalent to main.ts utilities

// GetAppRoot returns the application root directory
func GetAppRoot() string {
	if execPath, err := os.Executable(); err == nil {
		return filepath.Dir(execPath)
	}
	return "."
}

// GetPlatformInfo returns platform information
func GetPlatformInfo() map[string]interface{} {
	return map[string]interface{}{
		"platform":     runtime.GOOS,
		"arch":         runtime.GOARCH,
		"isWindows":    runtime.GOOS == "windows",
		"isMacintosh":  runtime.GOOS == "darwin",
		"isLinux":      runtime.GOOS == "linux",
		"numCPU":       runtime.NumCPU(),
		"goVersion":    runtime.Version(),
	}
}

// ParseArguments parses command line arguments
// Equivalent to parseMainProcessArgv in main.ts
func ParseArguments() map[string]interface{} {
	args := make(map[string]interface{})
	
	// Basic argument parsing (simplified)
	args["args"] = os.Args[1:]
	args["cwd"] = getCwd()
	
	return args
}

// getCwd returns current working directory
func getCwd() string {
	if cwd, err := os.Getwd(); err == nil {
		return cwd
	}
	return "."
}
